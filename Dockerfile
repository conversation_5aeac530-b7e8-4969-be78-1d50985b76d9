# Build stage
FROM node:18-alpine AS builder

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies with legacy peer deps to handle conflicts
RUN npm ci --legacy-peer-deps

# Copy source code
COPY . .

# Set build-time environment variables
ARG VITE_API_BASE_URL=http://172.16.20.49:8207
ENV VITE_API_BASE_URL=$VITE_API_BASE_URL

# Build the application (Vite will read VITE_* variables from environment)
RUN npm run build

# Production stage
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Install serve globally to serve the built app
RUN npm install -g serve

# Copy built app from builder stage
COPY --from=builder /app/dist ./dist

# Set default port
ENV PORT=8208

# Expose the port
EXPOSE $PORT

# Start the application using the PORT environment variable
CMD sh -c "serve -s dist -l $PORT"
