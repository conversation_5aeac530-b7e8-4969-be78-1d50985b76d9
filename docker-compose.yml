services:
  multi-tenant-frontend:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        - VITE_API_BASE_URL=${VITE_API_BASE_URL:-http://************:8207}
    container_name: multi-tenant-frontend
    network_mode: host
    environment:
      - NODE_ENV=production
      - PORT=${PORT:-8208}
      - TZ=${TZ:-UTC}
    env_file:
      - .env
    volumes:
      - /etc/localtime:/etc/localtime:ro
    restart: unless-stopped
    labels:
      - "com.project.name=multi-tenant-frontend"
