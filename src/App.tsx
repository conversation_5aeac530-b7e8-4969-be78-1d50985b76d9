import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { PublicLayout, ProtectedLayout } from "@/layouts";
import {
  AuthLogin,
  ProductsList,
  ProductsDashboard,
  ProductsNotFound,
  CreateTenantOnboarding,
  AnalyticsDashboard,
  SettingsDashboard,
} from "./pages";
import ProductDetail from "./pages/products/product_detail/product_detail";
import DefaultPromptPage from "./pages/products/eko_admin/DefaultPrompt";
import AdminContainer from "./pages/products/eko_admin/AdminContainer";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <Routes>
          <Route path="/" element={<Navigate to="/auth/login" replace />} />

          {/* Public Routes */}
          <Route
            path="/auth/login"
            element={
              <PublicLayout>
                <AuthLogin />
              </PublicLayout>
            }
          />

          {/* Protected Routes */}
          <Route
            path="/products"
            element={
              <ProtectedLayout>
                <ProductsList />
              </ProtectedLayout>
            }
          />
          <Route
            path="/product-info"
            element={
              <ProtectedLayout>
                <ProductDetail />
              </ProtectedLayout>
            }
          />
          <Route
            path="/eko-admin"
            element={
              <ProtectedLayout>
                {/* <DefaultPromptPage /> */}
                <AdminContainer />
              </ProtectedLayout>
            }
          />
          <Route
            path="/onboarding/create"
            element={
              <ProtectedLayout>
                <CreateTenantOnboarding />
              </ProtectedLayout>
            }
          />
          <Route
            path="/onboarding/create/:tenantId"
            element={
              <ProtectedLayout>
                <CreateTenantOnboarding />
              </ProtectedLayout>
            }
          />
          <Route
            path="/dashboard"
            element={
              <ProtectedLayout>
                <ProductsDashboard />
              </ProtectedLayout>
            }
          />
          <Route
            path="/dashboard/:slug"
            element={
              <ProtectedLayout>
                <ProductsDashboard />
              </ProtectedLayout>
            }
          />
          <Route
            path="/analytics"
            element={
              <ProtectedLayout>
                <AnalyticsDashboard />
              </ProtectedLayout>
            }
          />
          <Route
            path="/settings"
            element={
              <ProtectedLayout>
                <SettingsDashboard />
              </ProtectedLayout>
            }
          />

          {/* 404 Page */}
          <Route
            path="*"
            element={
              <PublicLayout>
                <ProductsNotFound />
              </PublicLayout>
            }
          />
        </Routes>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
