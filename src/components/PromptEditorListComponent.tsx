import React from "react";
import DeleteModal from "./DeleteModal";
import { Icon } from "@iconify/react";

export interface Prompt {
  _id: string;
  name: string;
  text: string;
  model: string;
  label?: string;
  description?: string;
}

interface Props {
  prompts: Prompt[];
  selectedPrompt: Prompt | null;
  isCreating?: boolean;
  loading: boolean;
  error: string | null;
  showDeleteModal: boolean;
  showPromptList: boolean;
  searchTerm: string;

  onChangeField: (field: keyof Prompt, value: string) => void;
  onSave: () => void;
  onDelete: () => void;
  onCreateNew: () => void;
  onCancelCreate?: () => void; // <-- add this prop to handle cancel
  onSelectPrompt: (prompt: Prompt) => void;

  setSearchTerm: (val: string) => void;
  setShowPromptList: (val: boolean) => void;
  setShowDeleteModal: (val: boolean) => void;
}

const PromptEditorListComponent: React.FC<Props> = ({
  prompts,
  selectedPrompt,
  isCreating = false,
  loading,
  error,
  showDeleteModal,
  showPromptList,
  searchTerm,

  onChangeField,
  onSave,
  onDelete,
  onCreateNew,
  onCancelCreate, // receive cancel handler
  onSelectPrompt,

  setSearchTerm,
  setShowPromptList,
  setShowDeleteModal,
}) => {
  if (loading && prompts.length === 0)
    return (
      <div className="p-8 text-center text-gray-700">Loading prompts...</div>
    );

  if (error)
    return (
      <div className="p-8 text-center text-gray-700">
        <p className="text-red-500">{error}</p>
      </div>
    );

  const filteredPrompts = prompts.filter(
    (prompt) =>
      prompt.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (prompt.label &&
        prompt.label.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  return (
    <div className="max-w-7xl mx-auto p-6">
      <DeleteModal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        onConfirm={onDelete}
        promptName={selectedPrompt?.label || selectedPrompt?.name || ""}
        isLoading={loading}
      />

      <div className="flex gap-6">
        <div
          className={`transition-all duration-300 ${
            showPromptList ? "w-2/3" : "w-full"
          }`}
        >
          <div className="bg-white border border-gray-200 rounded-xl shadow-sm p-6">
            {/* HEADER: Title + Add/Cancel toggle buttons */}
            <div className="flex items-center justify-between mb-6">
              <h1 className="text-2xl font-bold text-gray-800">
                {isCreating ? "Create New Prompt" : "Edit Prompt"}
              </h1>

              <div>
                {!isCreating ? (
                  <button
                    onClick={onCreateNew}
                    className="px-3 py-2  bg-black/70 text-white rounded-lg hover:bg-black/100 text-sm"
                    title="Add New Prompt"
                  >
                    <Icon icon="ic:round-add" className="text-xl" />
                  </button>
                ) : (
                  <button
                      onClick={onCancelCreate}
                      className="px-6 py-2 bg-red-100 text-red-500 rounded-lg hover:bg-red-200"
                  title="Cancel Creating New Prompt"
                >
                  Discard
                </button>

                )}
              </div>
            </div>

            {selectedPrompt && (
              <div className="space-y-6">
                {/* form inputs as before */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium mb-2 text-gray-700">
                      Name*
                    </label>
                    <input
                      type="text"
                      value={selectedPrompt.name}
                      onChange={(e) => onChangeField("name", e.target.value)}
                      className="w-full p-3 border border-gray-300 rounded-lg"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2 text-gray-700">
                      Label
                    </label>
                    <input
                      type="text"
                      value={selectedPrompt.label || ""}
                      onChange={(e) => onChangeField("label", e.target.value)}
                      className="w-full p-3 border border-gray-300 rounded-lg"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2 text-gray-700">
                    Model*
                  </label>
                  <input
                    type="text"
                    value={selectedPrompt.model}
                    onChange={(e) => onChangeField("model", e.target.value)}
                    className="w-full p-3 border border-gray-300 rounded-lg"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2 text-gray-700">
                    Prompt Text*
                  </label>
                  <textarea
                    rows={12}
                    value={selectedPrompt.text}
                    onChange={(e) => onChangeField("text", e.target.value)}
                    className="w-full p-3 border border-gray-300 rounded-lg font-mono"
                    required
                  />
                </div>

                <div className="flex justify-end space-x-4 pt-4">
                  {!isCreating && selectedPrompt._id && (
                    <button
                      onClick={() => setShowDeleteModal(true)}
                      className="px-6 py-2 bg-red-100 text-red-700 rounded-lg hover:bg-red-200"
                    >
                      Delete
                    </button>
                  )}
                  <button
                    onClick={onSave}
                    className={`px-6 py-2 rounded-lg ${
                      !selectedPrompt.name ||
                      !selectedPrompt.text ||
                      !selectedPrompt.model
                        ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                        : "bg-black/70 text-white hover:bg-black/100"
                    }`}
                    disabled={
                      !selectedPrompt.name ||
                      !selectedPrompt.text ||
                      !selectedPrompt.model
                    }
                  >
                    {loading ? "Saving..." : "Save Prompt"}
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Prompt List */}
        <div className="w-1/3">
          {showPromptList ? (
            <div className="bg-white border border-gray-200 rounded-xl shadow-sm h-[80vh] flex flex-col">
              {/* Header */}
              <div className="p-4 border-b flex items-center justify-between">
                <div className="relative w-2/3">
                  <input
                    type="text"
                    placeholder="Search prompts..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg"
                  />
                </div>
                <button
                  onClick={() => setShowPromptList(false)}
                  className="ml-2 px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 text-sm"
                >
                  Hide list
                </button>
              </div>

              {/* Scrollable List */}
              <div className="space-y-2 overflow-y-auto px-4 py-2 flex-1">
                {filteredPrompts.length > 0 ? (
                  filteredPrompts.map((prompt) => (
                    <div
                      key={prompt._id}
                      onClick={() => onSelectPrompt(prompt)}
                      className={`p-3 rounded-lg cursor-pointer transition-colors ${
                        selectedPrompt?._id === prompt._id
                          ? "bg-black/20 border border-black/60"
                          : "hover:bg-gray-60 border border-gray-200"
                      }`}
                    >
                      <div className="flex justify-between items-center">
                        <h3 className="font-medium text-gray-800">
                          {prompt.label || prompt.name}
                        </h3>
                        <span className="text-xs bg-gray-100 px-2 py-1 rounded text-gray-600">
                          {prompt.model}
                        </span>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-4 text-gray-500">
                    No prompts found
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="sticky top-6 flex justify-end">
              <button
                onClick={() => setShowPromptList(true)}
                className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 text-sm"
              >
                Show List
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default PromptEditorListComponent;
