// src/components/layout/SideNav.tsx

import React from "react";
import { useLocation, useNavigate } from "react-router-dom";
import {
  Home,
  Building2,
  BarChart3,
  Settings,
  Plus,
  Users,
  LogOut,
  X,
} from "lucide-react";
import { Lo<PERSON>, Button } from "@/components";
import { authService } from "@/services";

interface SideNavProps {
  user: any;
  selectedTenant: any;
  sidebarOpen: boolean;
  setSidebarOpen: (open: boolean) => void;
}

const SideNav: React.FC<SideNavProps> = ({
  user,
  selectedTenant,
  sidebarOpen,
  setSidebarOpen,
}) => {
  const navigate = useNavigate();
  const location = useLocation();

  const isAdmin = user?.role === "admin";

  const navigationItems = [
    { name: "Dashboard", href: "/dashboard", icon: Home, show: true },
    { name: "Products", href: "/products", icon: Building2, show: isAdmin },
    {
      name: "Create Business",
      href: "/onboarding/create",
      icon: Plus,
      show: isAdmin,
    },
    { name: "Analytics", href: "/analytics", icon: BarChart3, show: true },
    { name: "Settings", href: "/settings", icon: Settings, show: true },
  ].filter((item) => item.show);

  const isActivePath = (path: string) =>
    location.pathname === path || location.pathname.startsWith(path + "/");

  const handleLogout = () => {
    authService.logout();
    navigate("/auth/login");
  };

  return (
    <>
      <div
        className={`fixed inset-y-0 left-0 z-50 w-64 bg-gray-50 
          border-r border-border 
          flex flex-col h-screen
          ${sidebarOpen ? "" : "hidden"} 
          lg:block`}
      >
        {/* Header: Logo & Close Button */}
        <div className="flex items-center justify-between p-6 border-b border-border bg-gray-50">
          <div className="flex justify-center flex-1">
            <Logo variant="logo" className="w-80" size="xl" />
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setSidebarOpen(false)}
            className="lg:hidden ml-4"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* User Info */}
        <div className="p-6 border-b border-border bg-gray-50">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-primary rounded-full flex items-center justify-center">
              <Users className="h-5 w-5 text-primary-foreground" />
            </div>
            <div>
              <p className="text-sm font-medium text-foreground">
                {user.username}
              </p>
              <p className="text-xs text-muted-foreground capitalize">
                {user.role}
              </p>
            </div>
          </div>
          {selectedTenant && (
            <div className="mt-3 p-2 bg-muted rounded-md">
              <p className="text-xs text-muted-foreground">
                Current Selected Tenant
              </p>
              <p className="text-sm font-medium text-foreground">
                {selectedTenant.name}
              </p>
            </div>
          )}
        </div>

        {/* Navigation */}
        <nav className="flex-1 p-6 overflow-y-auto bg-gray-50">
          <ul className="space-y-2">
            {navigationItems.map(({ name, href, icon: Icon }) => {
              const isActive = isActivePath(href);
              return (
                <li key={name}>
                  <Button
                    variant="outline"
                    className={`w-full justify-start focus:outline-none focus:ring-0 ${
                      isActive
                        ? "bg-foreground text-background hover:bg-foreground hover:text-background"
                        : "text-muted-foreground hover:text-foreground hover:bg-muted border-transparent"
                    }`}
                    onClick={() => {
                      navigate(href);
                      setSidebarOpen(false);
                    }}
                  >
                    <Icon className="h-4 w-4 mr-3" />
                    {name}
                  </Button>
                </li>
              );
            })}
          </ul>
        </nav>

        {/* Logout fixed at bottom */}
        <div className="absolute bottom-0 left-0 w-full p-6 border-t border-border bg-gray-50">
          <Button
            variant="outline"
            className="w-full justify-start text-muted-foreground hover:text-foreground"
            onClick={handleLogout}
          >
            <LogOut className="h-4 w-4 mr-3" />
            Logout
          </Button>
        </div>
      </div>

      {/* Mobile Overlay */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}
    </>
  );
};

export default SideNav;
