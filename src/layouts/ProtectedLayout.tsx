// src/components/layout/ProtectedLayout.tsx

import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { authService } from "@/services";
import { Logo, Button } from "@/components";
import { Menu } from "lucide-react";
import SideNav from "../components/SideNav";

const ProtectedLayout: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [user, setUser] = useState<any>(null);
  const [selectedTenant, setSelectedTenant] = useState<any>(null);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    if (!authService.isAuthenticated()) {
      navigate("/auth/login");
      return;
    }

    const currentUser = authService.getCurrentUser();
    setUser(currentUser);

    const tenant = localStorage.getItem("selected_tenant");
    if (tenant) {
      setSelectedTenant(JSON.parse(tenant));
    }
  }, [navigate]);

  if (!user) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-foreground"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex bg-background overflow-hidden">
      {/* Sidebar */}
      <SideNav
        user={user}
        selectedTenant={selectedTenant}
        sidebarOpen={sidebarOpen}
        setSidebarOpen={setSidebarOpen}
      />

      {/* Main content */}
      <div className="flex-1 flex flex-col lg:ml-64 h-screen overflow-hidden">
        {/* Mobile Header */}
        <div className="lg:hidden bg-card border-b border-border p-4 shrink-0">
          <div className="flex items-center justify-between">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setSidebarOpen(true)}
            >
              <Menu className="h-5 w-5" />
            </Button>
            <div className="flex items-center space-x-2">
              <Logo variant="logo" size="sm" />
              <span className="font-semibold text-foreground">
                Multi-Tenant
              </span>
            </div>
            <div className="w-8" />
          </div>
        </div>

        {/* Scrollable page content */}
        <main className="flex-1 overflow-y-auto">{children}</main>
      </div>
    </div>
  );
};

export default ProtectedLayout;
