import React, { useState, useEffect } from 'react';
import { BarChart3, TrendingUp, Users, Activity, DollarSign, Eye } from 'lucide-react';
import { Button } from '@/components';

const AnalyticsDashboard: React.FC = () => {
  const [selectedTenant, setSelectedTenant] = useState<any>(null);

  useEffect(() => {
    const tenant = localStorage.getItem('selected_tenant');
    if (tenant) {
      setSelectedTenant(JSON.parse(tenant));
    }
  }, []);

  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-foreground mb-2">Analytics</h1>
        <p className="text-muted-foreground">
          Track your workspace performance and insights
        </p>
      </div>

      {/* Key Metrics */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4 mb-8">
        <div className="bg-card rounded-2xl p-6 border border-border">
          <div className="flex items-center justify-between mb-4">
            <div className="bg-blue-100 p-3 rounded-xl">
              <Users className="h-6 w-6 text-blue-600" />
            </div>
            <TrendingUp className="h-4 w-4 text-green-500" />
          </div>
          <h3 className="text-2xl font-bold text-foreground mb-1">1,234</h3>
          <p className="text-sm text-muted-foreground">Total Users</p>
          <p className="text-xs text-green-500 mt-1">+12% from last month</p>
        </div>

        <div className="bg-card rounded-2xl p-6 border border-border">
          <div className="flex items-center justify-between mb-4">
            <div className="bg-green-100 p-3 rounded-xl">
              <Activity className="h-6 w-6 text-green-600" />
            </div>
            <TrendingUp className="h-4 w-4 text-green-500" />
          </div>
          <h3 className="text-2xl font-bold text-foreground mb-1">89.2%</h3>
          <p className="text-sm text-muted-foreground">Active Sessions</p>
          <p className="text-xs text-green-500 mt-1">+5.4% from last week</p>
        </div>

        <div className="bg-card rounded-2xl p-6 border border-border">
          <div className="flex items-center justify-between mb-4">
            <div className="bg-purple-100 p-3 rounded-xl">
              <DollarSign className="h-6 w-6 text-purple-600" />
            </div>
            <TrendingUp className="h-4 w-4 text-green-500" />
          </div>
          <h3 className="text-2xl font-bold text-foreground mb-1">$24,567</h3>
          <p className="text-sm text-muted-foreground">Revenue</p>
          <p className="text-xs text-green-500 mt-1">+18% from last month</p>
        </div>

        <div className="bg-card rounded-2xl p-6 border border-border">
          <div className="flex items-center justify-between mb-4">
            <div className="bg-orange-100 p-3 rounded-xl">
              <Eye className="h-6 w-6 text-orange-600" />
            </div>
            <TrendingUp className="h-4 w-4 text-green-500" />
          </div>
          <h3 className="text-2xl font-bold text-foreground mb-1">45,678</h3>
          <p className="text-sm text-muted-foreground">Page Views</p>
          <p className="text-xs text-green-500 mt-1">+23% from last week</p>
        </div>
      </div>

      {/* Charts Section */}
      <div className="grid gap-6 lg:grid-cols-2 mb-8">
        {/* User Growth Chart */}
        <div className="bg-card rounded-2xl p-6 border border-border">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-foreground">User Growth</h3>
            <Button variant="outline" size="sm">
              View Details
            </Button>
          </div>
          <div className="h-64 bg-muted rounded-lg flex items-center justify-center">
            <div className="text-center">
              <BarChart3 className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
              <p className="text-muted-foreground">Chart visualization would go here</p>
            </div>
          </div>
        </div>

        {/* Revenue Chart */}
        <div className="bg-card rounded-2xl p-6 border border-border">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-foreground">Revenue Trends</h3>
            <Button variant="outline" size="sm">
              Export Data
            </Button>
          </div>
          <div className="h-64 bg-muted rounded-lg flex items-center justify-center">
            <div className="text-center">
              <TrendingUp className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
              <p className="text-muted-foreground">Revenue chart would go here</p>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="bg-card rounded-2xl p-6 border border-border">
        <h3 className="text-lg font-semibold text-foreground mb-6">Recent Activity</h3>
        <div className="space-y-4">
          <div className="flex items-center space-x-4 p-4 bg-background rounded-lg">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <div className="flex-1">
              <p className="text-sm font-medium text-foreground">New user registration</p>
              <p className="text-xs text-muted-foreground"><EMAIL> joined the platform</p>
            </div>
            <span className="text-xs text-muted-foreground">2 minutes ago</span>
          </div>
          <div className="flex items-center space-x-4 p-4 bg-background rounded-lg">
            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
            <div className="flex-1">
              <p className="text-sm font-medium text-foreground">Payment received</p>
              <p className="text-xs text-muted-foreground">$299 subscription payment processed</p>
            </div>
            <span className="text-xs text-muted-foreground">15 minutes ago</span>
          </div>
          <div className="flex items-center space-x-4 p-4 bg-background rounded-lg">
            <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
            <div className="flex-1">
              <p className="text-sm font-medium text-foreground">Feature usage spike</p>
              <p className="text-xs text-muted-foreground">Dashboard views increased by 45%</p>
            </div>
            <span className="text-xs text-muted-foreground">1 hour ago</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AnalyticsDashboard;
