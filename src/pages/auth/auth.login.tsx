import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import LoginForm from './components/auth.loginForm';
import { authService, LoginCredentials } from '@/services';
import { toast } from '@/hooks/use-toast';

const AuthLogin: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const navigate = useNavigate();

  useEffect(() => {
    // Redirect if already authenticated
    if (authService.isAuthenticated()) {
      if (authService.isAdmin()) {
        navigate('/products');
      } else {
        navigate('/dashboard');
      }
    }
  }, [navigate]);

  const handleLogin = async (credentials: LoginCredentials) => {
    setLoading(true);
    setError('');

    try {
      const response = await authService.login(credentials);
      
      toast({
        title: "Login Successful",
        description: `Welcome back, ${response.user.username}!`,
      });

      // Redirect based on user role
      if (response.user.role === 'admin') {
        navigate('/products');
      } else {
        navigate('/dashboard');
      }
    } catch (err: any) {
      setError(err.message);
      toast({
        variant: "destructive",
        title: "Login Failed",
        description: err.message,
      });
    } finally {
      setLoading(false);
    }
  };

  return <LoginForm onSubmit={handleLogin} loading={loading} error={error} />;
};

export default AuthLogin;
