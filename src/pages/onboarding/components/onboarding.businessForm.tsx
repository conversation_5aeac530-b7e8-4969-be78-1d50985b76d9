import authService from "@/services/auth/auth.service";
import React, { useState, useEffect } from "react";
import { Building2, <PERSON>R<PERSON>, ArrowLeft, Sparkles } from "lucide-react";
import { Tenant } from "@/services";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface BusinessFormProps {
  onNext: (businessName: string, businessType: string) => void;
  onBack?: () => void;
  selectedTenant?: Tenant | null;
  loading?: boolean;
}

interface OrgType {
  name: string;
  label: string;
}

const BusinessForm: React.FC<BusinessFormProps> = ({
  onNext,
  onBack,
  selectedTenant,
  loading = false,
}) => {
  const [businessName, setBusinessName] = useState("");
  const [businessType, setBusinessType] = useState("");
  const [orgTypes, setOrgTypes] = useState<OrgType[]>([]);
  const [isVisible, setIsVisible] = useState(false);
  const [isLoadingTypes, setIsLoadingTypes] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), 100);

    const fetchTypes = async () => {
      try {
        const orgTypesRaw = await authService.fetchOrgTypes();
        console.log("Fetched orgTypesRaw:", orgTypesRaw);

        // Defensive check: ensure it's an array
        if (Array.isArray(orgTypesRaw)) {
          setOrgTypes(orgTypesRaw);
        } else {
          console.error("Org types fetched is not an array:", orgTypesRaw);
          setOrgTypes([]); // fallback empty array
        }
      } catch (error) {
        console.error("Error fetching organization types:", error);
        setOrgTypes([]); // fallback empty array on error
      } finally {
        setIsLoadingTypes(false);
      }
    };

    fetchTypes();

    return () => clearTimeout(timer);
  }, []);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (businessName.trim() && businessType) {
      onNext(businessName.trim(), businessType);
    }
  };

  return (
    <div
      className={`transition-all duration-1000 transform ${
        isVisible ? "translate-y-0 opacity-100" : "translate-y-8 opacity-0"
      }`}
    >
      <div className="text-center mb-8">
        <div className="mx-auto w-20 h-20 bg-foreground rounded-full flex items-center justify-center mb-6 animate-pulse">
          <Building2 className="w-10 h-10 text-background" />
        </div>
        <h1 className="text-4xl font-bold text-foreground mb-4 animate-fade-in">
          {selectedTenant
            ? `Create for ${selectedTenant.name}`
            : "Welcome to Your Journey"}
        </h1>
        <p className="text-muted-foreground text-lg animate-fade-in-delay">
          {selectedTenant
            ? "Set up your new workspace"
            : "Let's start by getting to know your business"}
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="relative">
          <label className="block text-foreground font-medium mb-3 text-lg">
            What's your business or company name?
          </label>
          <div className="relative">
            <Sparkles className="absolute left-4 top-1/2 transform -translate-y-1/2 text-muted-foreground w-6 h-6" />
            <input
              type="text"
              value={businessName}
              onChange={(e) => setBusinessName(e.target.value)}
              placeholder="Enter your business name"
              className="w-full pl-14 pr-4 py-4 bg-card border border-border rounded-xl text-foreground placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-foreground focus:border-transparent transition-all duration-300 backdrop-blur-sm text-lg"
              required
              autoFocus
            />
          </div>
        </div>

        <div className="relative">
          <label className="block text-foreground font-medium mb-3 text-lg">
            What's your business or company Type?
          </label>
          <div className="relative">
            <Sparkles className="absolute left-4 top-1/2 transform -translate-y-1/2 text-muted-foreground w-6 h-6" />
            <Select value={businessType} onValueChange={setBusinessType}>
              <SelectTrigger className="w-full pl-14 pr-4 py-4 bg-card border border-border rounded-xl text-foreground placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-foreground focus:border-transparent transition-all duration-300 backdrop-blur-sm text-lg">
                <SelectValue placeholder="Select business type" />
              </SelectTrigger>
              <SelectContent>
                {isLoadingTypes ? (
                  <div className="py-2 text-center text-muted-foreground">
                    Loading types...
                  </div>
                ) : Array.isArray(orgTypes) && orgTypes.length > 0 ? (
                  orgTypes.map((type) => (
                    <SelectItem key={type.name} value={type.name}>
                      {type.label}
                    </SelectItem>
                  ))
                ) : (
                  <div className="py-2 text-center text-muted-foreground">
                    No organization types available
                  </div>
                )}
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="flex space-x-4">
          {onBack && (
            <button
              type="button"
              onClick={onBack}
              className="flex-1 bg-card hover:bg-muted text-foreground font-semibold py-4 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 flex items-center justify-center space-x-3 text-lg border border-border"
            >
              <ArrowLeft className="w-6 h-6" />
              <span>Back</span>
            </button>
          )}

          <button
            type="submit"
            disabled={!businessName.trim() || !businessType || loading}
            className={`${
              onBack ? "flex-1" : "w-full"
            } bg-foreground hover:bg-foreground/90 text-background font-semibold py-4 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center justify-center space-x-3 text-lg`}
          >
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-background"></div>
                <span>Processing...</span>
              </>
            ) : (
              <>
                <span>Continue</span>
                <ArrowRight className="w-6 h-6" />
              </>
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default BusinessForm;
