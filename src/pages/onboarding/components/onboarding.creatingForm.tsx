import React, { useState, useEffect, useRef } from 'react';
import { CheckCircle, Loader2, Building2, Users, Settings, Sparkles } from 'lucide-react';

interface CreatingFormProps {
  businessName: string;
  userName: string;
  onCreateTenant: () => void;
  loading: boolean;
}

const CreatingForm: React.FC<CreatingFormProps> = ({
  businessName,
  userName,
  onCreateTenant,
  loading
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [isVisible, setIsVisible] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const hasCalledRef = useRef(false);

  const steps = [
    { icon: Building2, text: 'Setting up your workspace...', delay: 0 },
    { icon: Users, text: 'Configuring user permissions...', delay: 800 },
    { icon: Settings, text: 'Initializing your environment...', delay: 1600 },
    { icon: Sparkles, text: 'Finalizing setup...', delay: 2400 }
  ];

  useEffect(() => {
    // Trigger animation on mount
    const timer = setTimeout(() => setIsVisible(true), 100);
    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    // Start the creation process immediately, but only once
    if (!hasCalledRef.current) {
      console.log('CreatingForm: Calling onCreateTenant for the first time');
      hasCalledRef.current = true;
      onCreateTenant();
    } else {
      console.log('CreatingForm: onCreateTenant already called, skipping');
    }
  }, []); // Empty dependency array to run only once

  useEffect(() => {
    if (!loading) return;

    // Animate through steps
    steps.forEach((_, index) => {
      setTimeout(() => {
        setCurrentStep(index);
      }, steps[index].delay);
    });

    // Show success after all steps
    setTimeout(() => {
      setShowSuccess(true);
    }, 3200);
  }, [loading]);

  return (
    <div className={`transition-all duration-1000 transform ${
      isVisible ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'
    }`}>
      <div className="text-center mb-8">
        <div className="mx-auto w-24 h-24 bg-foreground rounded-full flex items-center justify-center mb-6 relative overflow-hidden">
          {showSuccess && !loading ? (
            <CheckCircle className="w-12 h-12 text-background animate-scale-in" />
          ) : (
            <Loader2 className="w-12 h-12 text-background animate-spin" />
          )}
          
          {/* Animated ring */}
          <div className="absolute inset-0 rounded-full border-4 border-transparent border-t-background/30 animate-spin"></div>
        </div>
        
        <h1 className="text-4xl font-bold text-foreground mb-4 animate-fade-in">
          {showSuccess && !loading ? '🎉 Almost Ready!' : 'Creating Your Workspace'}
        </h1>
        
        <p className="text-muted-foreground text-lg animate-fade-in-delay">
          {showSuccess && !loading 
            ? `Welcome aboard, ${userName}! Your workspace is being prepared.`
            : `Setting up ${businessName} for you...`
          }
        </p>
      </div>

      {/* Progress Steps */}
      <div className="space-y-6 mb-8">
        {steps.map((step, index) => {
          const IconComponent = step.icon;
          const isActive = currentStep >= index;
          const isCompleted = showSuccess && !loading;
          
          return (
            <div
              key={index}
              className={`flex items-center space-x-4 transition-all duration-500 ${
                isActive ? 'opacity-100 translate-x-0' : 'opacity-30 translate-x-4'
              }`}
              style={{ transitionDelay: `${index * 200}ms` }}
            >
              <div className={`w-12 h-12 rounded-full flex items-center justify-center transition-all duration-300 ${
                isCompleted 
                  ? 'bg-foreground' 
                  : isActive 
                    ? 'bg-foreground animate-pulse' 
                    : 'bg-card border border-border'
              }`}>
                {isCompleted ? (
                  <CheckCircle className="w-6 h-6 text-background" />
                ) : (
                  <IconComponent className={`w-6 h-6 ${
                    isActive ? 'text-background' : 'text-muted-foreground'
                  }`} />
                )}
              </div>
              
              <div className="flex-1">
                <p className={`text-lg font-medium transition-colors duration-300 ${
                  isActive ? 'text-foreground' : 'text-muted-foreground'
                }`}>
                  {isCompleted ? 'Completed!' : step.text}
                </p>
              </div>
              
              {isActive && !isCompleted && (
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-foreground rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-foreground rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                  <div className="w-2 h-2 bg-foreground rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* Progress Bar */}
      <div className="w-full bg-card rounded-full h-3 mb-6 overflow-hidden">
        <div 
          className="h-full bg-foreground transition-all duration-1000 ease-out rounded-full relative"
          style={{ 
            width: showSuccess && !loading ? '100%' : `${((currentStep + 1) / steps.length) * 100}%` 
          }}
        >
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-background/20 to-transparent animate-pulse"></div>
        </div>
      </div>

      {/* Status Text */}
      <div className="text-center">
        <p className="text-muted-foreground">
          {showSuccess && !loading 
            ? 'Redirecting you to your new workspace...'
            : 'Please wait while we set everything up for you'
          }
        </p>
      </div>
    </div>
  );
};

export default CreatingForm;
