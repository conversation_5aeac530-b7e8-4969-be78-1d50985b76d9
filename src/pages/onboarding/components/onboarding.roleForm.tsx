import React, { useState } from "react";
import {
  Users,
  Crown,
  User,
  ArrowRight,
  ArrowLeft,
  CheckCircle,
  UserCheck,
} from "lucide-react";

interface RoleFormProps {
  onNext: (data: { name: string; role: string }) => void;
  onBack: () => void;
  loading?: boolean;
}

const roles = [
  {
    id: "admin",
    title: "Administrator",
    description: "Full access to manage everything",
    icon: Crown,
    color: "from-red-500 to-orange-600",
  },
  {
    id: "manager",
    title: "Manager",
    description: "Manage teams and projects",
    icon: Users,
    color: "from-blue-500 to-purple-600",
  },
  {
    id: "user",
    title: "Team Member",
    description: "Access to assigned projects",
    icon: User,
    color: "from-green-500 to-teal-600",
  },
];

const RoleForm: React.FC<RoleFormProps> = ({
  onNext,
  onBack,
  loading = false,
}) => {
  const [userName, setUserName] = useState("");
  const [selectedRole, setSelectedRole] = useState("admin"); // Default to admin
  const [isVisible, setIsVisible] = useState(false);

  React.useEffect(() => {
    // Trigger animation
    const timer = setTimeout(() => setIsVisible(true), 100);
    return () => clearTimeout(timer);
  }, []);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (userName.trim() && selectedRole) {
      onNext({ name: userName.trim(), role: selectedRole });
    }
  };

  return (
    <div
      className={`transition-all duration-1000 transform ${
        isVisible ? "translate-y-0 opacity-100" : "translate-y-8 opacity-0"
      }`}
    >
      <div className="text-center mb-8">
        <div className="mx-auto w-20 h-20 bg-foreground rounded-full flex items-center justify-center mb-6 animate-pulse">
          <Users className="w-10 h-10 text-background" />
        </div>
        <h1 className="text-4xl font-bold text-foreground mb-4">
          Almost there! Who are you?
        </h1>
        <p className="text-muted-foreground text-lg">
          Select your role to customize your experience
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Name Input */}
        <div className="relative">
          <label className="block text-foreground font-medium mb-3 text-lg">
            What's your user name?
          </label>
          <div className="relative">
            <UserCheck className="absolute left-4 top-1/2 transform -translate-y-1/2 text-muted-foreground w-6 h-6" />
            <input
              type="text"
              value={userName}
              onChange={(e) => setUserName(e.target.value)}
              placeholder="Enter your  username"
              className="w-full pl-14 pr-4 py-4 bg-card border border-border rounded-xl text-foreground placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-foreground focus:border-transparent transition-all duration-300 backdrop-blur-sm text-lg"
              required
              autoFocus
            />
          </div>
        </div>

        {/* Role Selection */}
        <div className="relative">
          <label className="block text-foreground font-medium mb-3 text-lg">
            Select your role
          </label>
          <div className="grid gap-4">
            {roles.map((role, index) => {
              const IconComponent = role.icon;
              return (
                <div
                  key={role.id}
                  className={`transition-all duration-500 transform ${
                    isVisible
                      ? "translate-x-0 opacity-100"
                      : "translate-x-8 opacity-0"
                  }`}
                  style={{ transitionDelay: `${index * 150}ms` }}
                >
                  <label
                    className={`block cursor-pointer transition-all duration-300 transform hover:scale-105 ${
                      selectedRole === role.id ? "scale-105" : ""
                    }`}
                  >
                    <input
                      type="radio"
                      name="role"
                      value={role.id}
                      checked={selectedRole === role.id}
                      onChange={(e) => setSelectedRole(e.target.value)}
                      className="sr-only"
                    />
                    <div
                      className={`relative p-6 rounded-xl border-2 transition-all duration-300 ${
                        selectedRole === role.id
                          ? "border-foreground bg-card shadow-2xl"
                          : "border-border bg-card/50 hover:bg-card"
                      } backdrop-blur-sm`}
                    >
                      <div className="flex items-center space-x-4">
                        <div
                          className={`w-12 h-12 bg-gradient-to-r ${role.color} rounded-lg flex items-center justify-center`}
                        >
                          <IconComponent className="w-6 h-6 text-white" />
                        </div>
                        <div className="flex-1">
                          <h3 className="text-xl font-semibold text-foreground">
                            {role.title}
                          </h3>
                          <p className="text-muted-foreground">
                            {role.description}
                          </p>
                        </div>
                        {selectedRole === role.id && (
                          <CheckCircle className="w-6 h-6 text-foreground animate-scale-in" />
                        )}
                      </div>
                    </div>
                  </label>
                </div>
              );
            })}
          </div>
        </div>

        <div className="flex space-x-4">
          <button
            type="button"
            onClick={onBack}
            className="flex-1 bg-card hover:bg-muted text-foreground font-semibold py-4 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 flex items-center justify-center space-x-3 text-lg border border-border"
          >
            <ArrowLeft className="w-6 h-6" />
            <span>Back</span>
          </button>

          <button
            type="submit"
            disabled={!userName.trim() || !selectedRole || loading}
            className="flex-1 bg-foreground hover:bg-foreground/90 text-background font-semibold py-4 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center justify-center space-x-3 text-lg"
          >
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-background"></div>
                <span>Creating...</span>
              </>
            ) : (
              <>
                <span>Create Account</span>
                <ArrowRight className="w-6 h-6" />
              </>
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default RoleForm;
