import React, { useState, useEffect } from 'react';
import { <PERSON>h, <PERSON>R<PERSON>, ArrowLeft, Wand2 } from 'lucide-react';

interface SlugFormProps {
  businessName: string;
  onNext: (slug: string) => void;
  onBack: () => void;
  loading?: boolean;
}

const SlugForm: React.FC<SlugFormProps> = ({ businessName, onNext, onBack, loading = false }) => {
  const [slug, setSlug] = useState('');
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Generate initial slug from business name
    const initialSlug = businessName
      .toLowerCase()
      .replace(/[^a-z0-9\s]/g, '')
      .replace(/\s+/g, '-')
      .substring(0, 30);
    setSlug(initialSlug);

    // Trigger animation
    const timer = setTimeout(() => setIsVisible(true), 100);
    return () => clearTimeout(timer);
  }, [businessName]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (slug.trim()) {
      onNext(slug.trim());
    }
  };

  const handleSlugChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
      .toLowerCase()
      .replace(/[^a-z0-9-]/g, '')
      .replace(/--+/g, '-');
    setSlug(value);
  };

  return (
    <div className={`transition-all duration-1000 transform ${
      isVisible ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'
    }`}>
      <div className="text-center mb-8">
        <div className="mx-auto w-20 h-20 bg-foreground rounded-full flex items-center justify-center mb-6 animate-pulse">
          <Hash className="w-10 h-10 text-background" />
        </div>
        <h1 className="text-4xl font-bold text-foreground mb-4">
          Perfect! Now let's create your unique identifier
        </h1>
        <p className="text-muted-foreground text-lg">
          This will be your company's fancy name in the system
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="relative">
          <label className="block text-foreground font-medium mb-3 text-lg">
            Your Company Slug
          </label>
          <div className="relative">
            <Wand2 className="absolute left-4 top-1/2 transform -translate-y-1/2 text-muted-foreground w-6 h-6" />
            <input
              type="text"
              value={slug}
              onChange={handleSlugChange}
              placeholder="your-company-name"
              className="w-full pl-14 pr-4 py-4 bg-card border border-border rounded-xl text-foreground placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-foreground focus:border-transparent transition-all duration-300 backdrop-blur-sm text-lg font-mono"
              required
              autoFocus
            />
          </div>
          <p className="text-muted-foreground text-sm mt-2">
            This will be used in URLs and system references. Only lowercase letters, numbers, and hyphens allowed.
          </p>
        </div>

        <div className="flex space-x-4">
          <button
            type="button"
            onClick={onBack}
            className="flex-1 bg-card hover:bg-muted text-foreground font-semibold py-4 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 flex items-center justify-center space-x-3 text-lg border border-border"
          >
            <ArrowLeft className="w-6 h-6" />
            <span>Back</span>
          </button>

          <button
            type="submit"
            disabled={!slug.trim() || loading}
            className="flex-1 bg-foreground hover:bg-foreground/90 text-background font-semibold py-4 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center justify-center space-x-3 text-lg"
          >
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-background"></div>
                <span>Processing...</span>
              </>
            ) : (
              <>
                <span>Continue</span>
                <ArrowRight className="w-6 h-6" />
              </>
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default SlugForm;
