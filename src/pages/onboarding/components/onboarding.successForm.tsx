import React, { useState, useEffect } from 'react';
import { CheckCircle, ExternalLink, Copy, ArrowRight } from 'lucide-react';

interface SuccessFormProps {
  invitationLink: string;
  businessName: string;
  companySlug: string;
  userName?: string;
  userRole?: string;
  onNavigate: () => void;
}

const SuccessForm: React.FC<SuccessFormProps> = ({
  invitationLink,
  businessName,
  companySlug,
  userName,
  userRole,
  onNavigate
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [countdown, setCountdown] = useState(5);
  const [copied, setCopied] = useState(false);

  useEffect(() => {
    // Trigger animation
    const timer = setTimeout(() => setIsVisible(true), 100);
    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    // Auto-navigate countdown
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          onNavigate();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [onNavigate]);

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(invitationLink);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy:', err);
    }
  };

  const openLink = () => {
    window.open(invitationLink, '_blank');
  };

  return (
    <div className={`transition-all duration-1000 transform ${
      isVisible ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'
    }`}>
      <div className="text-center mb-8">
        <div className="mx-auto w-24 h-24 bg-foreground rounded-full flex items-center justify-center mb-6 animate-bounce">
          <CheckCircle className="w-12 h-12 text-background" />
        </div>
        <h1 className="text-4xl font-bold text-foreground mb-4 animate-fade-in">
          🎉 Congratulations, {userName}!
        </h1>
        <p className="text-muted-foreground text-lg animate-fade-in-delay">
          Welcome to being a member of <strong>{businessName}</strong>! Your workspace is ready.
        </p>
      </div>

      <div className="space-y-6">
        <div className="bg-card backdrop-blur-sm rounded-xl p-6 border border-border">
          <h3 className="text-xl font-semibold text-foreground mb-4">Your Account Details</h3>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-muted-foreground">Business Name:</span>
              <span className="text-foreground font-medium">{businessName}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-muted-foreground">Company Slug:</span>
              <span className="text-foreground font-medium font-mono">{companySlug}</span>
            </div>
            {userName && (
              <div className="flex justify-between items-center">
                <span className="text-muted-foreground">Your Name:</span>
                <span className="text-foreground font-medium">{userName}</span>
              </div>
            )}
            {userRole && (
              <div className="flex justify-between items-center">
                <span className="text-muted-foreground">Your Role:</span>
                <span className="text-foreground font-medium capitalize">{userRole}</span>
              </div>
            )}
          </div>
        </div>

        <div className="bg-card backdrop-blur-sm rounded-xl p-6 border border-border">
          <h3 className="text-xl font-semibold text-foreground mb-4">Invitation Link</h3>
          <p className="text-muted-foreground mb-4">
            Share this link with your team members to invite them to your workspace:
          </p>
          <div className="flex space-x-2">
            <input
              type="text"
              value={invitationLink}
              readOnly
              className="flex-1 px-4 py-3 bg-background border border-border rounded-lg text-foreground text-sm font-mono focus:outline-none focus:ring-2 focus:ring-foreground"
            />
            <button
              onClick={copyToClipboard}
              className="px-4 py-3 bg-foreground hover:bg-foreground/90 text-background rounded-lg transition-colors duration-200 flex items-center space-x-2"
              title="Copy to clipboard"
            >
              <Copy className="w-4 h-4" />
              <span className="hidden sm:inline">{copied ? 'Copied!' : 'Copy'}</span>
            </button>
            <button
              onClick={openLink}
              className="px-4 py-3 bg-foreground hover:bg-foreground/90 text-background rounded-lg transition-colors duration-200 flex items-center space-x-2"
              title="Open link"
            >
              <ExternalLink className="w-4 h-4" />
              <span className="hidden sm:inline">Open</span>
            </button>
          </div>
        </div>

        <div className="text-center">
          <button
            onClick={onNavigate}
            className="bg-foreground hover:bg-foreground/90 text-background font-semibold py-4 px-8 rounded-xl transition-all duration-300 transform hover:scale-105 flex items-center justify-center space-x-3 text-lg mx-auto shadow-lg hover:shadow-xl"
          >
            <span>Enter Your Workspace</span>
            <ArrowRight className="w-6 h-6" />
          </button>
          <p className="text-muted-foreground mt-4">
            Auto-redirecting in <span className="font-bold text-foreground">{countdown}</span> seconds...
          </p>
        </div>
      </div>
    </div>
  );
};

export default SuccessForm;
