import React, { useState } from 'react';
import { Building2, ArrowR<PERSON>, Plus, ChevronDown } from 'lucide-react';
import { Tenant } from '@/services';

interface TenantSelectFormProps {
  tenants: Tenant[];
  onSelect: (tenant: Tenant) => void;
  onCreateNew: () => void;
  loading?: boolean;
}

const TenantSelectForm: React.FC<TenantSelectFormProps> = ({ 
  tenants, 
  onSelect, 
  onCreateNew, 
  loading = false 
}) => {
  const [selectedTenant, setSelectedTenant] = useState<Tenant | null>(null);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isVisible, setIsVisible] = useState(false);

  React.useEffect(() => {
    // Trigger animation on mount
    const timer = setTimeout(() => setIsVisible(true), 100);
    return () => clearTimeout(timer);
  }, []);

  const handleTenantSelect = (tenant: Tenant) => {
    setSelectedTenant(tenant);
    setIsDropdownOpen(false);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (selectedTenant) {
      onSelect(selectedTenant);
    }
  };

  return (
    <div className={`transition-all duration-1000 transform ${
      isVisible ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'
    }`}>
      <div className="text-center mb-8">
        <div className="mx-auto w-20 h-20 bg-foreground rounded-full flex items-center justify-center mb-6 animate-pulse">
          <Building2 className="w-10 h-10 text-background" />
        </div>
        <h1 className="text-4xl font-bold text-foreground mb-4 animate-fade-in">
          Select Your Business
        </h1>
        <p className="text-muted-foreground text-lg animate-fade-in-delay">
          Choose an existing business or create a new one
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="relative">
          <label className="block text-foreground font-medium mb-3 text-lg">
            Available Businesses
          </label>
          
          {tenants.length > 0 ? (
            <div className="relative">
              <button
                type="button"
                onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                className="w-full px-4 py-4 bg-card border border-border rounded-xl text-foreground focus:outline-none focus:ring-2 focus:ring-foreground focus:border-transparent transition-all duration-300 backdrop-blur-sm text-lg flex items-center justify-between"
              >
                <span className={selectedTenant ? 'text-foreground' : 'text-muted-foreground'}>
                  {selectedTenant ? selectedTenant.name : 'Select a business...'}
                </span>
                <ChevronDown className={`w-5 h-5 transition-transform duration-200 ${
                  isDropdownOpen ? 'rotate-180' : ''
                }`} />
              </button>

              {isDropdownOpen && (
                <div className="absolute z-10 w-full mt-2 bg-card border border-border rounded-xl shadow-lg max-h-60 overflow-y-auto">
                  {tenants.map((tenant) => (
                    <button
                      key={tenant.id}
                      type="button"
                      onClick={() => handleTenantSelect(tenant)}
                      className="w-full px-4 py-3 text-left hover:bg-muted transition-colors duration-200 first:rounded-t-xl last:rounded-b-xl flex items-center space-x-3"
                    >
                      <div className="w-8 h-8 bg-foreground rounded-lg flex items-center justify-center">
                        <Building2 className="w-4 h-4 text-background" />
                      </div>
                      <div>
                        <div className="font-medium text-foreground">{tenant.name}</div>
                        <div className="text-sm text-muted-foreground font-mono">/{tenant.slug}</div>
                      </div>
                    </button>
                  ))}
                </div>
              )}
            </div>
          ) : (
            <div className="text-center py-8">
              <p className="text-muted-foreground mb-4">No businesses found</p>
            </div>
          )}
        </div>

        <div className="flex space-x-4">
          <button
            type="button"
            onClick={onCreateNew}
            className="flex-1 bg-card hover:bg-muted text-foreground font-semibold py-4 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 flex items-center justify-center space-x-3 text-lg border border-border"
          >
            <Plus className="w-6 h-6" />
            <span>Create New Business</span>
          </button>
          
          {tenants.length > 0 && (
            <button
              type="submit"
              disabled={!selectedTenant || loading}
              className="flex-1 bg-foreground hover:bg-foreground/90 text-background font-semibold py-4 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center justify-center space-x-3 text-lg"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-background"></div>
                  <span>Processing...</span>
                </>
              ) : (
                <>
                  <span>Continue</span>
                  <ArrowRight className="w-6 h-6" />
                </>
              )}
            </button>
          )}
        </div>
      </form>
    </div>
  );
};

export default TenantSelectForm;
