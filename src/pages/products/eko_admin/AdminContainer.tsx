import React, { useState } from "react";
import { Icon } from "@iconify/react";
import DefaultPromptPage from "./DefaultPrompt";
import RequirementPage from "./RequirementPage";
import SetupSettingPage from "./SetupSettingPage";
import ExtraEko from "./ExtraEko";
// import ToolsPage from "./ToolsPage";

const tabs = [
  { id: "prompt", label: "Prompt", icon: "solar:code-bold" },
  { id: "requirement", label: "Requirement", icon: "solar:document-text-bold" },
  { id: "setup", label: "Setup Setting", icon: "solar:settings-bold" },
  { id: "more", label: "More", icon: "solar:widget-bold" },
];

const AdminContainer = () => {
  const [activeTab, setActiveTab] = useState("prompt");

  const renderContent = () => {
    switch (activeTab) {
      case "prompt":
        return <DefaultPromptPage />;
      case "requirement":
        return <RequirementPage />;
      case "setup":
        return <SetupSettingPage />;
      case "more":
        return <ExtraEko />;

        return <DefaultPromptPage />;
      default:
        return <DefaultPromptPage />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Fixed Header + Tabs */}
      <div className="sticky top-0 z-20 bg-gray-50 w-full ">
        <div className="max-w-7xl mx-auto px-4 py-1">
          <div className="flex flex-col mb-4">
            <h1 className="text-2xl font-bold text-gray-900 mt-4">
              Admin Dashboard
            </h1>
            <p className="text-gray-600 text-sm">
              Manage Eko's system configuration and settings
            </p>
          </div>

          <div className="bg-white rounded-xl p-2 shadow-sm border border-gray-200 mb-2">
            <div className="flex space-x-2">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center gap-2 px-6 py-3 rounded-xl font-medium transition-all duration-200 ${
                    activeTab === tab.id
                      ? "bg-black/70 text-white shadow-lg transform scale-105"
                      : "text-gray-600 hover:text-gray-900 hover:bg-gray-50"
                  }`}
                >
                  <Icon icon={tab.icon} className="text-lg" />
                  {tab.label}
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Scrollable Content */}
      <div className="max-w-7xl mx-auto px-4 py-8 animate-fadeIn">
        {renderContent()}
      </div>

      <style jsx>{`
        @keyframes fadeIn {
          from {
            opacity: 0;
            transform: translateY(20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        .animate-fadeIn {
          animation: fadeIn 0.5s ease-out;
        }
      `}</style>
    </div>
  );
};

export default AdminContainer;
