import React, { useEffect, useState } from "react";
import baseHttp from "../../../services/http/base.http";
import DeleteModal from "../../../components/DeleteModal";
import PromptEditorListComponent from "@/components/PromptEditorListComponent";

interface Prompt {
  _id: string;
  name: string;
  text: string;
  model: string;
  label?: string;
}

const DefaultPromptPage = () => {
  const [prompts, setPrompts] = useState<Prompt[]>([]);
  const [formState, setFormState] = useState<
    Omit<Prompt, "_id"> & { _id?: string }
  >({
    name: "",
    text: "",
    model: "",
    label: "",
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isCreating, setIsCreating] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showPromptList, setShowPromptList] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");

  useEffect(() => {
    const fetchPrompts = async () => {
      setLoading(true);
      setError(null);
      try {
        const response = await baseHttp.get("/eko-info/default-prompt");
        const fetchedPrompts = response.data || [];
        setPrompts(fetchedPrompts);
        if (fetchedPrompts.length > 0) {
          setFormState({ ...fetchedPrompts[0] });
        }
      } catch (err: any) {
        setError(err.message || "Failed to fetch prompts");
      } finally {
        setLoading(false);
      }
    };
    fetchPrompts();
  }, []);

  const filteredPrompts = prompts.filter(
    (prompt) =>
      prompt.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (prompt.label &&
        prompt.label.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const handleChange = (field: keyof Prompt, value: string) => {
    setFormState((prev) => ({ ...prev, [field]: value }));
  };

  const handleSelectPrompt = (prompt: Prompt) => {
    setFormState({ ...prompt });
    setIsCreating(false);
  };

  const handleCreateNew = () => {
    setFormState({ name: "", text: "", model: "", label: "" });
    setIsCreating(true);
  };

  // Cancel creation: revert to first prompt or empty if none
  const handleCancelCreate = () => {
    if (prompts.length > 0) {
      setFormState({ ...prompts[0] });
    } else {
      setFormState({ name: "", text: "", model: "", label: "" });
    }
    setIsCreating(false);
  };

  const handleSave = async () => {
    try {
      setLoading(true);
      if (isCreating) {
        const response = await baseHttp.post("/eko-info/default-prompt", formState);
        const newPrompt = response.data;
        setPrompts((prev) => [...prev, newPrompt]);
        setFormState({ ...newPrompt });
        setIsCreating(false);
      } else {
        await baseHttp.put(`/eko-info/default-prompt/${formState._id}`, formState);
        setPrompts((prev) =>
          prev.map((p) =>
            p._id === formState._id ? ({ ...formState } as Prompt) : p
          )
        );
      }
    } catch (err: any) {
      setError(err.message || "Failed to save prompt");
    } finally {
      setLoading(false);
    }
  };

  const confirmDelete = () => {
    setShowDeleteModal(true);
  };

  const handleDelete = async () => {
    if (!formState._id) return;
    try {
      setLoading(true);
      await baseHttp.delete(`/eko-info/default-prompt/${formState._id}`);
      const updated = prompts.filter((p) => p._id !== formState._id);
      setPrompts(updated);
      setFormState(updated[0] || { name: "", text: "", model: "", label: "" });
      setShowDeleteModal(false);
    } catch (err: any) {
      setError(err.message || "Failed to delete prompt");
    } finally {
      setLoading(false);
    }
  };

  if (loading && prompts.length === 0) {
    return (
      <div className="p-8 text-center text-gray-700">Loading prompts...</div>
    );
  }

  if (error) {
    return (
      <div className="p-8 text-center text-gray-700">
        <p className="text-red-500">{error}</p>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto p-1">
      <DeleteModal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        onConfirm={handleDelete}
        promptName={formState.label || formState.name}
        isLoading={loading}
      />

      <PromptEditorListComponent
        prompts={filteredPrompts}
        selectedPrompt={formState}
        isCreating={isCreating}
        loading={loading}
        error={error}
        showDeleteModal={showDeleteModal}
        showPromptList={showPromptList}
        searchTerm={searchTerm}
        onChangeField={handleChange}
        onSave={handleSave}
        onDelete={confirmDelete}
        onCreateNew={handleCreateNew}
        onCancelCreate={handleCancelCreate}  // <-- Pass cancel handler here
        onSelectPrompt={handleSelectPrompt}
        setSearchTerm={setSearchTerm}
        setShowPromptList={setShowPromptList}
        setShowDeleteModal={setShowDeleteModal}
      />
    </div>
  );
};

export default DefaultPromptPage;
