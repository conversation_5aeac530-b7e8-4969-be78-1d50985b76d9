import { useState } from "react";
import { Icon } from "@iconify/react";
import baseHttp from "@/services/baseHttp";

interface TokenResponse {
  id: string;
  access_token: string;
  token_type: string;
  username: string;
  role: string;
  tenant_label: string;
  tenant_slug: string;
  token_validity: {
    days: number;
    hours: number;
    minutes: number;
    seconds: number;
    total_seconds: number;
  };
  expires_at: string;
}

const formatTenantName = (dbName: string) => {
  return dbName.replace(/^eko_/, '').replace(/_db$/, '');
};

const ExtraEko: React.FC<{ tenant_db_name: string }> = ({
  tenant_db_name,
}) => {
  // User creation states
  const [createuserOpen, setCreateuserOpen] = useState(true);
  const [userName, setUserName] = useState("");
  const [userPassword, setUserPassword] = useState("");
  const [isBot, setIsBot] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [createUserError, setCreateUserError] = useState<string | null>(null);
  const [createUserSuccess, setCreateUserSuccess] = useState<string | null>(null);

  // Token generation states
  const [generateTokenOpen, setGenerateTokenOpen] = useState(false);
  const [tokenUsername, setTokenUsername] = useState("");
  const [tokenPassword, setTokenPassword] = useState("");
  const [daysToExtend, setDaysToExtend] = useState(30);
  const [isGeneratingToken, setIsGeneratingToken] = useState(false);
  const [tokenError, setTokenError] = useState<string | null>(null);
  const [generatedTokenData, setGeneratedTokenData] = useState<TokenResponse | null>(null);
  
  const handleCreateuser = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsCreating(true);
    setCreateUserError(null);
    setCreateUserSuccess(null);
    
    try {
      const response = await baseHttp.post("/eko-info/user/register", {
        db_name: tenant_db_name,
        username: userName,
        password: userPassword,
        role: "admin",
        isBot: isBot
      });
      
      setCreateUserSuccess("User created successfully!");
      setUserName("");
      setUserPassword("");
      setIsBot(false);
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || error.message || "Failed to create user";
      setCreateUserError(errorMessage);
    } finally {
      setIsCreating(false);
    }
  };
  
  const handleGenerateToken = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsGeneratingToken(true);
    setTokenError(null);
    setGeneratedTokenData(null);
    
    try {
      const response = await baseHttp.post<TokenResponse>("https://eko-api2.nextai.asia/extended_token", {
        username: tokenUsername,
        password: tokenPassword,
        client_id: formatTenantName(tenant_db_name),
        days: daysToExtend
      });
      
      
      setGeneratedTokenData(response.data);
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || error.message || "Failed to generate token";
      setTokenError(errorMessage);
    } finally {
      setIsGeneratingToken(false);
    }
  };



  
  return (
    <div className="space-y-4">
      {/* Create user Section */}
      <div className="bg-white rounded-2xl p-6 border border-gray-200 shadow-sm">
        <button
          className="w-full flex justify-between items-center"
          onClick={() => setCreateuserOpen(!createuserOpen)}
        >
          <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
            <Icon icon="solar:rouser-bold" className="text-black-500" />
            Create User
          </h3>
          <Icon 
            icon={createuserOpen ? "mdi:chevron-up" : "mdi:chevron-down"} 
            className="w-5 h-5 text-gray-500"
          />
        </button>
        
        {createuserOpen && (
          <form onSubmit={handleCreateuser} className="mt-6 space-y-4">
            {createUserError && (
              <div className="bg-red-50 border-l-4 border-red-500 p-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <Icon icon="heroicons-outline:exclamation-circle" className="h-5 w-5 text-red-500" />
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-red-700">{createUserError}</p>
                  </div>
                </div>
              </div>
            )}
            
            {createUserSuccess && (
              <div className="bg-green-50 border-l-4 border-green-500 p-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <Icon icon="heroicons-outline:check-circle" className="h-5 w-5 text-green-500" />
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-green-700">{createUserSuccess}</p>
                  </div>
                </div>
              </div>
            )}
            
            <div className="grid grid-cols-1 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">UserName</label>
                <input
                  type="text"
                  value={userName}
                  onChange={(e) => setUserName(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"
                  placeholder="Enter user name"
                  required
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Password</label>
                <input
                  type="password"
                  value={userPassword}
                  onChange={(e) => setUserPassword(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"
                  placeholder="Enter password"
                  required
                />
              </div>

              <div className="flex items-center">
                <input
                  id="is-bot-checkbox"
                  type="checkbox"
                  checked={isBot}
                  onChange={(e) => setIsBot(e.target.checked)}
                  className="w-4 h-4 text-black border-gray-300 rounded focus:ring-black"
                />
                <label htmlFor="is-bot-checkbox" className="ml-2 text-sm font-medium text-gray-700">
                  Is this a user bot?
                </label>
              </div>
            </div>
            
            <div className="flex justify-end">
              <button 
                type="submit" 
                className="px-6 py-2 bg-black text-white font-medium rounded-lg hover:bg-gray-800 transition-colors disabled:opacity-50"
                disabled={isCreating}
              >
                {isCreating ? (
                  <span className="flex items-center">
                    <Icon icon="eos-icons:loading" className="mr-2" />
                    Creating...
                  </span>
                ) : "Create user"}
              </button>
            </div>
          </form>
        )}
      </div>
      
      {/* Generate Access Token Section */}
      <div className="bg-white rounded-2xl p-6 border border-gray-200 shadow-sm">
        <button
          className="w-full flex justify-between items-center"
          onClick={() => setGenerateTokenOpen(!generateTokenOpen)}
        >
          <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
            <Icon icon="solar:key-bold" className="text-black-500" />
            Generate Access Token
          </h3>
          <Icon 
            icon={generateTokenOpen ? "mdi:chevron-up" : "mdi:chevron-down"} 
            className="w-7 h-7 text-white bg-black/60 p-1 rounded-full"
          />
        </button>
        
        {generateTokenOpen && (
          <form onSubmit={handleGenerateToken} className="mt-6 space-y-4">
            {tokenError && (
              <div className="bg-red-50 border-l-4 border-red-500 p-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <Icon icon="heroicons-outline:exclamation-circle" className="h-5 w-5 text-red-500" />
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-red-700">{tokenError}</p>
                  </div>
                </div>
              </div>
            )}
            
            {generatedTokenData && (
              <div className="bg-green-50 border-l-4 border-green-500 p-4 rounded-md">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <Icon icon="heroicons-outline:check-circle" className="h-5 w-5 text-green-500" />
                  </div>
                  <div className="ml-3 flex-1">
                    <h3 className="text-sm font-medium text-green-800 mb-2">Token generated successfully!</h3>
                    
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Access Token</label>
                        <div className="bg-gray-100 p-3 rounded-md overflow-x-auto">
                          <code className="text-sm text-gray-800 break-all">{generatedTokenData.access_token}</code>
                        </div>
                        <p className="mt-1 text-xs text-gray-500">
                          Token type: <span className="font-medium">{generatedTokenData.token_type}</span>
                        </p>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <h4 className="text-sm font-medium text-gray-700 mb-2">Token Information</h4>
                          <dl className="space-y-2 text-sm">
                            <div className="flex justify-between">
                              <dt className="text-gray-500">Username:</dt>
                              <dd className="font-medium">{generatedTokenData.username}</dd>
                            </div>
                            <div className="flex justify-between">
                              <dt className="text-gray-500">Role:</dt>
                              <dd className="font-medium">{generatedTokenData.role}</dd>
                            </div>
                            <div className="flex justify-between">
                              <dt className="text-gray-500">Tenant:</dt>
                              <dd className="font-medium">{generatedTokenData.tenant_label} ({generatedTokenData.tenant_slug})</dd>
                            </div>
                          </dl>
                        </div>

                        <div>
                          <h4 className="text-sm font-medium text-gray-700 mb-2">Validity</h4>
                          <dl className="space-y-2 text-sm">
                            <div className="flex justify-between">
                              <dt className="text-gray-500">Expires at:</dt>
                              <dd className="font-medium">
                                {new Date(generatedTokenData.expires_at).toLocaleString()}
                              </dd>
                            </div>
                            <div className="flex justify-between">
                              <dt className="text-gray-500">Days valid:</dt>
                              <dd className="font-medium">{generatedTokenData.token_validity.days}</dd>
                            </div>
                            <div className="flex justify-between">
                              <dt className="text-gray-500">Total seconds:</dt>
                              <dd className="font-medium">{generatedTokenData.token_validity.total_seconds}</dd>
                            </div>
                          </dl>
                        </div>
                      </div>

                      <button
                        onClick={() => {
                          navigator.clipboard.writeText(generatedTokenData.access_token);
                          // Add toast notification here if needed
                        }}
                        className="mt-2 px-4 py-2 bg-blue-50 text-blue-600 text-sm font-medium rounded-md hover:bg-blue-100 transition-colors"
                      >
                        <Icon icon="heroicons-outline:clipboard-copy" className="inline mr-2" />
                        Copy Token to Clipboard
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}
            
            <div className="grid grid-cols-1 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Username</label>
                <input
                  type="text"
                  value={tokenUsername}
                  onChange={(e) => setTokenUsername(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"
                  placeholder="Enter username"
                  required
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Password</label>
                <input
                  type="password"
                  value={tokenPassword}
                  onChange={(e) => setTokenPassword(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"
                  placeholder="Enter password"
                  required
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Tenant Name</label>
                <input
                  type="text"
                  value={formatTenantName(tenant_db_name)}
                  readOnly
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg bg-gray-100 cursor-not-allowed"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Days to Extend</label>
                <input
                  type="number"
                  value={daysToExtend}
                  onChange={(e) => setDaysToExtend(Number(e.target.value))}
                  min="1"
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"
                  required
                />
              </div>
            </div>
            
            <div className="flex justify-end">
              <button 
                type="submit" 
                className="px-6 py-2 bg-black text-white font-medium rounded-lg hover:bg-gray-800 transition-colors disabled:opacity-50"
                disabled={isGeneratingToken}
              >
                {isGeneratingToken ? (
                  <span className="flex items-center">
                    <Icon icon="eos-icons:loading" className="mr-2" />
                    Generating...
                  </span>
                ) : "Generate Token"}
              </button>
            </div>
          </form>
        )}
      </div>
    </div>
  );
};

export default ExtraEko;