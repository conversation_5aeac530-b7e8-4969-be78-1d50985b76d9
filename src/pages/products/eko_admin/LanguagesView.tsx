import React, { useState } from "react";
import { Icon } from "@iconify/react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Plus, Trash2, Globe, CheckCircle, ChevronRight } from "lucide-react";

const LanguagesView = ({ languages, onAddLanguage, onDeleteLanguage }) => {
  const [newLanguage, setNewLanguage] = useState("");
  const [isAdding, setIsAdding] = useState(false);

  const handleAddLanguage = async () => {
    if (!newLanguage.trim()) return;
    setIsAdding(true);
    try {
      await onAddLanguage(newLanguage.trim());
      setNewLanguage("");
    } finally {
      setIsAdding(false);
    }
  };

  return (
    <Card className="shadow-sm">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Globe className="w-5 h-5 text-blue-600" />
          <span>Language Settings</span>
        </CardTitle>
        <p className="text-sm text-muted-foreground">
          Manage available languages for your application
        </p>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Current Languages */}
          <div>
            <h3 className="text-sm font-medium mb-3">Active Languages</h3>
            {languages.length > 0 ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                {languages.map((language) => (
                  <div
                    key={language}
                    className="flex items-center justify-between p-3 border rounded-lg hover:border-blue-300 transition-colors group"
                  >
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 rounded-full bg-blue-50 flex items-center justify-center">
                        <span className="text-sm font-medium text-blue-600">
                          {language.slice(0, 2).toUpperCase()}
                        </span>
                      </div>
                      <div>
                        <p className="font-medium">{language}</p>
                        <p className="text-xs text-muted-foreground">
                          Configured
                        </p>
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="opacity-0 group-hover:opacity-100 h-8 w-8 p-0"
                      onClick={() => onDeleteLanguage(language)}
                    >
                      <Trash2 className="h-4 w-4 text-red-500" />
                    </Button>
                  </div>
                ))}
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center py-8 border rounded-lg bg-muted/50">
                <Globe className="w-8 h-8 text-muted-foreground mb-2" />
                <p className="text-sm text-muted-foreground">
                  No languages configured yet
                </p>
              </div>
            )}
          </div>

          {/* Add Language Section */}
          <div className="border-t pt-6">
            <h3 className="text-sm font-medium mb-3">Add New Language</h3>
            <div className="flex gap-2">
              <Input
                placeholder="Enter language name (e.g. French)"
                value={newLanguage}
                onChange={(e) => setNewLanguage(e.target.value)}
                onKeyDown={(e) => e.key === "Enter" && handleAddLanguage()}
                className="max-w-md"
              />
              <Button
                onClick={handleAddLanguage}
                disabled={!newLanguage.trim() || isAdding}
              >
                {isAdding ? (
                  <>
                    <svg
                      className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                    Adding...
                  </>
                ) : (
                  <>
                    <Plus className="w-4 h-4 mr-2" />
                    Add Language
                  </>
                )}
              </Button>
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              Use standard language names (English, Spanish, French, etc.)
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default LanguagesView;