import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Popconfirm } from "@/components/ui/popconfirm";
import { Plus, Edit, Trash2, AlertCircle, X, Eye } from "lucide-react";
import OrgTypeModal from "./modal/OrgTypeModal";
import GoalModal from "./modal/GoalModal";
import GoalDetailModal from "./modal/GoalDetailModal";

const OrgTypesView = ({
  orgTypes = [],
  onAddOrgType = () => {},
  onUpdateOrgType = () => {},
  onDeleteOrgType = () => {},
  onAddGoal = () => {},
  onUpdateGoal = () => {},
  onDeleteGoal = () => {},
  error = null,
  setError = () => {},
}) => {
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isGoalDetailModalOpen, setIsGoalDetailModalOpen] = useState(false);
  const [isGoalAddModalOpen, setIsGoalAddModalOpen] = useState(false);
  const [isGoalEditModalOpen, setIsGoalEditModalOpen] = useState(false);
  const [currentOrgType, setCurrentOrgType] = useState(null);
  const [currentGoal, setCurrentGoal] = useState(null);

  const openEditModal = (orgType) => {
    setCurrentOrgType(orgType);
    setIsEditModalOpen(true);
  };

  const closeAllModals = () => {
    setIsAddModalOpen(false);
    setIsEditModalOpen(false);
    setIsGoalDetailModalOpen(false);
    setIsGoalAddModalOpen(false);
    setIsGoalEditModalOpen(false);
    setCurrentOrgType(null);
    setCurrentGoal(null);
  };

  const handleUpdate = async (updatedOrgType) => {
    try {
      await onUpdateOrgType(updatedOrgType);
      closeAllModals();
    } catch (err) {
      setError(err.message);
    }
  };

  const handleGoalEdit = (goal) => {
    setCurrentGoal(goal);
    setIsGoalDetailModalOpen(false);
    setIsGoalEditModalOpen(true);
  };

  const handleGoalAdd = (orgType) => {
    setCurrentOrgType(orgType);
    setCurrentGoal(null);
    setIsGoalAddModalOpen(true);
  };

  const handleGoalDetail = (orgType, goal) => {
    setCurrentOrgType(orgType);
    setCurrentGoal(goal);
    setIsGoalDetailModalOpen(true);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold text-foreground">
            Organization Types
          </h2>
          <p className="text-muted-foreground">
            Manage organization types and their agent goals
          </p>
        </div>
        <Button onClick={() => setIsAddModalOpen(true)} className="gap-2">
          <Plus className="h-4 w-4" /> Add Organization Type
        </Button>
      </div>

      {error && (
        <div className="flex items-center gap-2 p-4 bg-muted border border-border rounded-lg">
          <AlertCircle className="h-5 w-5 text-foreground flex-shrink-0" />
          <span className="text-foreground flex-1">{error}</span>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setError(null)}
            className="text-foreground hover:text-foreground/80"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      )}

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {orgTypes.map((orgType) => (
          <Card key={orgType.name} className="hover:shadow-md transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">{orgType.label}</CardTitle>
                <div className="flex gap-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => openEditModal(orgType)}
                    className="h-8 w-8 p-0"
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Popconfirm
                    title="Delete Organization Type"
                    description={`Are you sure you want to delete "${orgType.label}"? This action cannot be undone and will remove all associated goals.`}
                    onConfirm={() => onDeleteOrgType(orgType.name)}
                    okText="Delete"
                    cancelText="Cancel"
                  >
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0 text-destructive hover:text-destructive/80"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </Popconfirm>
                </div>
              </div>
              <CardDescription>
                <code className="text-xs bg-muted px-2 py-1 rounded">
                  {orgType.name}
                </code>
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Agent Goals</span>
                  <Badge variant="secondary">
                    {orgType.agent_goals.length}
                  </Badge>
                </div>

                {orgType.agent_goals.length > 0 ? (
                  <div className="space-y-2">
                    {orgType.agent_goals.map((goal) => (
                      <div
                        key={goal.prompt_type}
                        className="flex items-center justify-between p-2 bg-muted rounded-lg hover:bg-muted/80 transition-colors cursor-pointer"
                        onClick={() => handleGoalDetail(orgType, goal)}
                      >
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium truncate">
                            {goal.prompt_type}
                          </p>
                          <p className="text-xs text-muted-foreground truncate">
                            {goal.prompt_summary}
                          </p>
                        </div>
                        <Eye className="h-4 w-4 text-muted-foreground ml-2" />
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-sm text-muted-foreground">
                    No goals configured
                  </p>
                )}

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleGoalAdd(orgType)}
                  className="w-full gap-2"
                >
                  <Plus className="h-4 w-4" /> Add Goal
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <OrgTypeModal
        isOpen={isAddModalOpen}
        onClose={closeAllModals}
        onSave={onAddOrgType}
        mode="add"
      />

      <OrgTypeModal
        isOpen={isEditModalOpen}
        onClose={closeAllModals}
        orgType={currentOrgType}
        onSave={handleUpdate}
        mode="edit"
      />

      <GoalModal
        key={`goal-add-${currentOrgType?.name || "new"}`}
        isOpen={isGoalAddModalOpen}
        onClose={closeAllModals}
        orgTypeName={currentOrgType?.name}
        onSave={onAddGoal}
        mode="add"
      />

      <GoalModal
        key={`goal-edit-${currentGoal?.prompt_type || "edit"}`}
        isOpen={isGoalEditModalOpen}
        onClose={closeAllModals}
        goal={currentGoal}
        orgTypeName={currentOrgType?.name}
        onSave={onUpdateGoal}
        mode="edit"
      />

      <GoalDetailModal
        isOpen={isGoalDetailModalOpen}
        onClose={closeAllModals}
        goal={currentGoal}
        orgTypeName={currentOrgType?.name}
        onEdit={handleGoalEdit}
        onDelete={(promptType) => {
          onDeleteGoal(currentOrgType?.name, promptType);
          closeAllModals();
        }}
      />
    </div>
  );
};

export default OrgTypesView;