import React, { useState, useEffect } from "react";
import { Icon } from "@iconify/react";
import baseHttp from "../../../services/http/base.http";
import { toast } from "react-toastify";
import { motion, AnimatePresence } from "framer-motion";

const RequirementPage = () => {
  const [requirements, setRequirements] = useState({
    required_prompt: [],
    required_tools: [],
    required_settings: [],
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [newItem, setNewItem] = useState({ value: "" });
  const [editingItem, setEditingItem] = useState({
    category: null,
    index: null,
    value: "",
  });
  const [activeCategory, setActiveCategory] = useState("required_prompt");
  const [showAddForm, setShowAddForm] = useState(false);

  const categoryData = {
    required_prompt: {
      title: "Required Prompts",
      icon: "solar:code-square-bold",
      color: "bg-black/10 text-black/80",
      accent: "bg-black/50",
    },
    required_tools: {
      title: "Required Tools",
      icon: "solar:widget-bold",
      color: "bg-black/10 text-black/80",
      accent: "bg-black/50",
    },
    required_settings: {
      title: "Required Settings",
      icon: "solar:settings-bold",
      color: "bg-black/10 text-black/80",
      accent: "bg-black/50",
    },
  };

  const fetchRequirements = async () => {
    try {
      setLoading(true);
      const response = await baseHttp.get("/eko-info/tenant-requirement");
      setRequirements(response.data);
      setError(null);
    } catch (err) {
      setError(err.message || "Failed to fetch requirements");
      toast.error("Failed to fetch requirements");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchRequirements();
  }, []);

  const handleAddItem = async () => {
    if (!newItem.value.trim()) return;

    try {
      const updatedRequirements = {
        ...requirements,
        [activeCategory]: [
          ...requirements[activeCategory],
          newItem.value.trim(),
        ],
      };

      setRequirements(updatedRequirements);
      setNewItem({ value: "" });
      setShowAddForm(false);

      await baseHttp.put("/eko-info/tenant-requirement", updatedRequirements);
      toast.success("Item added successfully");
    } catch (err) {
      fetchRequirements();
      toast.error("Failed to add item");
    }
  };

  const handleEditItem = async () => {
    if (!editingItem.value.trim()) return;

    try {
      const updatedRequirements = { ...requirements };
      updatedRequirements[editingItem.category][editingItem.index] =
        editingItem.value.trim();

      setRequirements(updatedRequirements);
      setEditingItem({ category: null, index: null, value: "" });

      await baseHttp.put("/eko-info/tenant-requirement", updatedRequirements);
      toast.success("Item updated successfully");
    } catch (err) {
      fetchRequirements();
      toast.error("Failed to update item");
    }
  };

  const handleDeleteItem = async (category, index) => {
    try {
      const updatedRequirements = { ...requirements };
      updatedRequirements[category] = requirements[category].filter(
        (_, i) => i !== index
      );

      setRequirements(updatedRequirements);
      await baseHttp.put("/eko-info/tenant-requirement", updatedRequirements);
      toast.success("Item deleted successfully");
    } catch (err) {
      fetchRequirements();
      toast.error("Failed to delete item");
    }
  };

  const renderCategoryTabs = () => (
    <div className="flex space-x-4 mb-6 border-b border-gray-200 pb-2">
      {Object.keys(categoryData).map((category) => (
        <button
          key={category}
          onClick={() => {
            setActiveCategory(category);
            setShowAddForm(false);
          }}
          className={`px-4 py-2 rounded-t-lg font-medium transition-all duration-300 relative ${
            activeCategory === category
              ? "text-gray-900 font-semibold"
              : "text-gray-500 hover:text-gray-700"
          }`}
        >
          {categoryData[category].title}
          {activeCategory === category && (
            <motion.div
              layoutId="activeTabIndicator"
              className={`absolute bottom-0 left-0 right-0 h-1 ${categoryData[category].accent}`}
              initial={false}
              transition={{ type: "spring", stiffness: 300, damping: 30 }}
            />
          )}
        </button>
      ))}
    </div>
  );

  const renderItem = (item, index, category) => (
    <motion.div
      key={`${category}-${index}`}
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, x: -50 }}
      transition={{ duration: 0.2 }}
      className="flex items-center justify-between p-4 bg-white rounded-lg shadow-xs hover:shadow-sm border border-gray-100 mb-2 transition-all duration-200 hover:border-gray-200"
    >
      {editingItem.category === category && editingItem.index === index ? (
        <div className="flex-1 flex items-center gap-3">
          <input
            type="text"
            value={editingItem.value}
            onChange={(e) =>
              setEditingItem({ ...editingItem, value: e.target.value })
            }
            onKeyDown={(e) => {
              if (e.key === "Escape") {
                setEditingItem({ category: null, index: null, value: "" });
              }
            }}
            className="flex-1 px-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent"
            autoFocus
          />
          <div className="flex gap-1">
            <button
              onClick={handleEditItem}
              className="p-2 text-white bg-green-500 rounded-lg hover:bg-green-600 transition-colors"
            >
              <Icon icon="solar:check-circle-bold" className="text-xl" />
            </button>
            <button
              onClick={() =>
                setEditingItem({ category: null, index: null, value: "" })
              }
              className="p-2 text-gray-500 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
            >
              <Icon icon="solar:close-circle-bold" className="text-xl" />
            </button>
          </div>
        </div>
      ) : (
        <>
          <div className="flex items-center gap-3">
            <div
              className={`w-8 h-8 rounded-full ${categoryData[category].color} flex items-center justify-center`}
            >
              <Icon icon={categoryData[category].icon} className="text-lg" />
            </div>
            <span className="font-medium text-gray-800">{item}</span>
          </div>
          <div className="flex gap-2">
            <button
              onClick={() => setEditingItem({ category, index, value: item })}
              className="p-2 text-black/60 hover:bg-black/10 rounded-lg transition-colors"
            >
              <Icon icon="solar:pen-bold" className="text-lg" />
            </button>
            <button
              onClick={() => handleDeleteItem(category, index)}
              className="p-2 text-red-500 hover:bg-red-50 rounded-lg transition-colors"
            >
              <Icon icon="solar:trash-bin-trash-bold" className="text-lg" />
            </button>
          </div>
        </>
      )}
    </motion.div>
  );

  const renderActiveCategory = () => {
    const currentCategory = categoryData[activeCategory];

    return (
      <div className="space-y-4">
        <div
          className={`p-4 rounded-xl ${currentCategory.color} bg-opacity-20 flex justify-between items-center`}
        >
          <div className="flex items-center gap-3">
            <div
              className={`w-12 h-12 rounded-lg ${currentCategory.color} flex items-center justify-center`}
            >
              <Icon icon={currentCategory.icon} className="text-2xl" />
            </div>
            <div>
              <h3 className="text-xl font-bold text-gray-900">
                {currentCategory.title}
              </h3>
              <p className="text-gray-600">
                Manage {currentCategory.title.toLowerCase()} for your tenant.
              </p>
            </div>
          </div>

          {!showAddForm && (
            <button
              onClick={() => setShowAddForm(true)}
              className="px-4 py-2 bg-black text-white rounded-lg hover:bg-black/80 transition"
            >
              Add new item
            </button>
          )}
        </div>

        {showAddForm && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            className="p-4 bg-white rounded-xl shadow-xs border border-gray-100"
          >
            <h4 className="font-medium text-gray-800 mb-3">
              Add New {currentCategory.title}
            </h4>
            <div className="flex gap-3">
              <input
                type="text"
                value={newItem.value}
                onChange={(e) => setNewItem({ value: e.target.value })}
                placeholder={`Enter new ${currentCategory.title.toLowerCase()}...`}
                className="flex-1 px-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent"
                onKeyPress={(e) => e.key === "Enter" && handleAddItem()}
              />
              <button
                onClick={handleAddItem}
                disabled={!newItem.value.trim()}
                className={`px-4 py-2 rounded-lg font-medium flex items-center gap-2 transition-colors ${
                  newItem.value.trim()
                    ? `text-white ${currentCategory.accent} hover:opacity-90`
                    : "bg-gray-200 text-gray-400 cursor-not-allowed"
                }`}
              >
                <Icon icon="solar:add-circle-bold" />
                Add
              </button>
              <button
                onClick={() => setShowAddForm(false)}
                className="px-4 py-2 text-gray-500 hover:bg-gray-100 rounded-lg transition-colors"
              >
                Cancel
              </button>
            </div>
          </motion.div>
        )}

        <AnimatePresence>
          {requirements[activeCategory].length > 0 ? (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.3 }}
              className="space-y-2"
            >
              {requirements[activeCategory].map((item, index) =>
                renderItem(item, index, activeCategory)
              )}
            </motion.div>
          ) : (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="p-8 text-center bg-gray-50 rounded-xl border border-dashed border-gray-300"
            >
              <Icon
                icon="solar:info-circle-bold"
                className="text-4xl text-gray-400 mx-auto mb-3"
              />
              <h4 className="text-gray-500 font-medium">
                No {currentCategory.title.toLowerCase()} found
              </h4>
              <p className="text-gray-400">
                Add your first item using the button above
              </p>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
          className="w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full"
        />
      </div>
    );
  }

  if (error) {
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="bg-red-50 border border-red-200 rounded-xl p-6 text-center max-w-md mx-auto"
      >
        <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <Icon icon="solar:danger-bold" className="text-red-500 text-3xl" />
        </div>
        <h3 className="text-lg font-medium text-red-800 mb-2">
          Error Loading Requirements
        </h3>
        <p className="text-red-600 mb-4">{error}</p>
        <button
          onClick={fetchRequirements}
          className="px-5 py-2.5 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors font-medium flex items-center gap-2 mx-auto"
        >
          <Icon icon="solar:refresh-bold" />
          Try Again
        </button>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
      className="space-y-6"
    >
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-1">
          Requirements Management
        </h2>
        <p className="text-gray-500">
          Configure system default requirements. These are basic thing added to
          Tenant while creating new one.
        </p>
      </div>

      {renderCategoryTabs()}
      {renderActiveCategory()}
    </motion.div>
  );
};

export default RequirementPage;
