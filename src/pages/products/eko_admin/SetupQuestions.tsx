import { useState, useEffect } from "react";
import baseHttp from "../../../services/http/base.http";
import { Button } from "../../../components/ui/button";
import { Input } from "../../../components/ui/input";
import { Textarea } from "../../../components/ui/textarea";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "../../../components/ui/card";
import {
  PlusCircle,
  Trash2,
  Edit2,
  Save,
  X,
  ChevronDown,
  ChevronUp,
} from "lucide-react";
import { toast } from "sonner";

const SetupQuestions = () => {
  const [questions, setQuestions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [expandedCards, setExpandedCards] = useState({});
  const [editingId, setEditingId] = useState(null);
  const [newQuestion, setNewQuestion] = useState({
    type: "",
    questions: [],
    dummy_data: "",
    dummy_metadata: { category: "", type: "" },
  });
  const [currentQuestion, setCurrentQuestion] = useState("");
  const [isCreating, setIsCreating] = useState(false);

  // Toggle card expansion
  const toggleCard = (id) => {
    setExpandedCards((prev) => ({
      ...prev,
      [id]: !prev[id],
    }));
  };

  // Fetch data
  useEffect(() => {
    fetchSetupQuestions();
  }, []);

  const fetchSetupQuestions = async () => {
    try {
      setLoading(true);
      const response = await baseHttp.get("/eko-info/setup-questions");
      setQuestions(response.data.data || []);
      // Initialize all cards as expanded
      const expanded = {};
      response.data.data.forEach((_, index) => {
        expanded[index] = true;
      });
      setExpandedCards(expanded);
    } catch (error) {
      toast.error("Failed to fetch setup questions");
    } finally {
      setLoading(false);
    }
  };

  // CRUD operations
  const handleAddQuestion = () => {
    if (!currentQuestion.trim()) return;
    setNewQuestion((prev) => ({
      ...prev,
      questions: [...prev.questions, currentQuestion.trim()],
    }));
    setCurrentQuestion("");
  };

  const handleCreate = async () => {
    try {
      await baseHttp.post("/setup-questions", newQuestion);
      toast.success("Question set created successfully");
      fetchSetupQuestions();
      setIsCreating(false);
      resetForm();
    } catch (error) {
      toast.error("Failed to create question set");
    }
  };

  const handleUpdate = async (question) => {
    try {
      await baseHttp.put("/setup-questions", question);
      toast.success("Question set updated successfully");
      setEditingId(null);
      fetchSetupQuestions();
    } catch (error) {
      toast.error("Failed to update question set");
    }
  };

  const handleDelete = async (type) => {
    try {
      await baseHttp.delete("/setup-questions");
      toast.success("Question set deleted successfully");
      fetchSetupQuestions();
    } catch (error) {
      toast.error("Failed to delete question set");
    }
  };

  const resetForm = () => {
    setNewQuestion({
      type: "",
      questions: [],
      dummy_data: "",
      dummy_metadata: { category: "", type: "" },
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 p-6">
      <div className="max-w-6xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-800">Setup Questions</h1>
            <p className="text-gray-600 mt-1">
              Manage question sets for different scenarios
            </p>
          </div>
          <Button
            onClick={() => setIsCreating(!isCreating)}
            className="gap-2"
          >
            <PlusCircle className="h-4 w-4" />
            {isCreating ? "Cancel" : "New Question Set"}
          </Button>
        </div>

        {/* Creation Card */}
        {isCreating && (
          <Card className="border border-gray-200 shadow-sm">
            <CardHeader>
              <CardTitle className="text-lg">Create New Question Set</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">
                    Type
                  </label>
                  <Input
                    value={newQuestion.type}
                    onChange={(e) =>
                      setNewQuestion({
                        ...newQuestion,
                        type: e.target.value,
                        dummy_metadata: {
                          ...newQuestion.dummy_metadata,
                          type: e.target.value,
                        },
                      })
                    }
                    placeholder="e.g., marketing_sales"
                    className="bg-white"
                  />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">
                    Category
                  </label>
                  <Input
                    value={newQuestion.dummy_metadata.category}
                    onChange={(e) =>
                      setNewQuestion({
                        ...newQuestion,
                        dummy_metadata: {
                          ...newQuestion.dummy_metadata,
                          category: e.target.value,
                        },
                      })
                    }
                    placeholder="e.g., Apple Products"
                    className="bg-white"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">
                  Dummy Data
                </label>
                <Textarea
                  value={newQuestion.dummy_data}
                  onChange={(e) =>
                    setNewQuestion({
                      ...newQuestion,
                      dummy_data: e.target.value,
                    })
                  }
                  placeholder="Enter detailed dummy data..."
                  rows={4}
                  className="bg-white"
                />
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">
                  Questions
                </label>
                <div className="flex gap-2">
                  <Input
                    value={currentQuestion}
                    onChange={(e) => setCurrentQuestion(e.target.value)}
                    placeholder="Enter a question"
                    className="bg-white flex-1"
                    onKeyDown={(e) => e.key === "Enter" && handleAddQuestion()}
                  />
                  <Button
                    onClick={handleAddQuestion}
                    disabled={!currentQuestion.trim()}
                    variant="outline"
                  >
                    <PlusCircle className="h-4 w-4" />
                  </Button>
                </div>

                {newQuestion.questions.length > 0 && (
                  <div className="mt-3 space-y-2 max-h-60 overflow-y-auto p-2 border rounded-lg bg-white">
                    {newQuestion.questions.map((q, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between p-2 hover:bg-gray-50 rounded"
                      >
                        <span className="text-sm">{q}</span>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() =>
                            setNewQuestion({
                              ...newQuestion,
                              questions: newQuestion.questions.filter(
                                (_, i) => i !== index
                              ),
                            })
                          }
                          className="text-red-500 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </CardContent>
            <CardFooter className="flex justify-end gap-2 border-t px-6 py-4">
              <Button variant="outline" onClick={() => setIsCreating(false)}>
                Cancel
              </Button>
              <Button onClick={handleCreate}>Create Set</Button>
            </CardFooter>
          </Card>
        )}

        {/* Question Sets List */}
        {loading ? (
          <div className="flex justify-center py-12">
            <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-primary"></div>
          </div>
        ) : questions.length === 0 ? (
          <Card className="text-center py-12 bg-white">
            <p className="text-gray-500">No question sets found</p>
          </Card>
        ) : (
          <div className="space-y-4">
            {questions.map((item, index) => (
              <Card
                key={index}
                className="border border-gray-200 shadow-sm overflow-hidden"
              >
                <div
                  className="flex items-center justify-between p-4 hover:bg-gray-50 cursor-pointer"
                  onClick={() => toggleCard(index)}
                >
                  <div className="flex items-center gap-3">
                    <div className="font-medium text-gray-800">{item.type}</div>
                    <div className="text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded-full">
                      {item.questions.length} questions
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      toggleCard(index);
                    }}
                  >
                    {expandedCards[index] ? (
                      <ChevronUp className="h-4 w-4" />
                    ) : (
                      <ChevronDown className="h-4 w-4" />
                    )}
                  </Button>
                </div>

                {expandedCards[index] && (
                  <div className="border-t">
                    <CardContent className="p-6 space-y-4">
                      <div className="grid md:grid-cols-2 gap-6">
                        <div>
                          <h3 className="font-medium text-gray-700 mb-2">
                            Questions
                          </h3>
                          <div className="space-y-2">
                            {item.questions.map((q, qIndex) => (
                              <div
                                key={qIndex}
                                className="flex items-start gap-2 p-2 bg-gray-50 rounded"
                              >
                                <div className="text-sm flex-1">{q}</div>
                              </div>
                            ))}
                          </div>
                        </div>

                        <div>
                          <h3 className="font-medium text-gray-700 mb-2">
                            Dummy Data
                          </h3>
                          <div className="p-3 bg-gray-50 rounded text-sm whitespace-pre-line max-h-60 overflow-y-auto">
                            {item.dummy_data}
                          </div>
                        </div>
                      </div>

                      <div className="pt-4 border-t">
                        <h3 className="font-medium text-gray-700 mb-2">
                          Metadata
                        </h3>
                        <div className="flex gap-4">
                          <div>
                            <span className="text-sm text-gray-500">Type:</span>
                            <span className="text-sm font-medium ml-2">
                              {item.dummy_metadata.type}
                            </span>
                          </div>
                          <div>
                            <span className="text-sm text-gray-500">
                              Category:
                            </span>
                            <span className="text-sm font-medium ml-2">
                              {item.dummy_metadata.category}
                            </span>
                          </div>
                        </div>
                      </div>
                    </CardContent>

                    <CardFooter className="flex justify-end gap-2 border-t px-6 py-4 bg-gray-50">
                      {editingId === index ? (
                        <>
                          <Button
                            variant="outline"
                            onClick={() => setEditingId(null)}
                          >
                            Cancel
                          </Button>
                          <Button onClick={() => handleUpdate(item)}>
                            <Save className="h-4 w-4 mr-2" />
                            Save Changes
                          </Button>
                        </>
                      ) : (
                        <>
                          <Button
                            variant="outline"
                            onClick={() => setEditingId(index)}
                          >
                            <Edit2 className="h-4 w-4 mr-2" />
                            Edit
                          </Button>
                          <Button
                            variant="destructive"
                            onClick={() => handleDelete(item.type)}
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete
                          </Button>
                        </>
                      )}
                    </CardFooter>
                  </div>
                )}
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default SetupQuestions;