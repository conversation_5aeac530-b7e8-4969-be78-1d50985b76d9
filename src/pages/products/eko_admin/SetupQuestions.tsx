import { useState, useEffect } from "react";
import baseHttp from "../../../services/http/base.http";
import { Button } from "../../../components/ui/button";
import { Input } from "../../../components/ui/input";
import { Textarea } from "../../../components/ui/textarea";
import { Badge } from "../../../components/ui/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "../../../components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "../../../components/ui/dialog";
import { Label } from "../../../components/ui/label";
import { Popconfirm } from "../../../components/ui/popconfirm";
import {
  PlusCircle,
  Trash2,
  Edit2,
  Save,
  <PERSON>,
  ChevronDown,
  ChevronUp,
  FileText,
  Database,
  Settings,
  AlertTriangle,
  <PERSON>rkles,
  Eye,
  Plus,
} from "lucide-react";
import { toast } from "sonner";

const SetupQuestions = () => {
  const [questions, setQuestions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [expandedCards, setExpandedCards] = useState({});
  const [editingId, setEditingId] = useState(null);
  const [newQuestion, setNewQuestion] = useState({
    type: "",
    questions: [],
    dummy_data: "",
    dummy_metadata: { category: "", type: "" },
  });
  const [currentQuestion, setCurrentQuestion] = useState("");
  const [isCreating, setIsCreating] = useState(false);

  // Modal states
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [editingItem, setEditingItem] = useState(null);
  const [editFormData, setEditFormData] = useState({
    type: "",
    questions: [],
    dummy_data: "",
    dummy_metadata: { category: "", type: "" },
  });
  const [editCurrentQuestion, setEditCurrentQuestion] = useState("");
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [viewingItem, setViewingItem] = useState(null);

  // Toggle card expansion
  const toggleCard = (id) => {
    setExpandedCards((prev) => ({
      ...prev,
      [id]: !prev[id],
    }));
  };

  // Modal functions
  const openEditModal = (item, index) => {
    setEditingItem({ ...item, index });
    setEditFormData({
      type: item.type,
      questions: [...item.questions],
      dummy_data: item.dummy_data,
      dummy_metadata: { ...item.dummy_metadata },
    });
    setIsEditModalOpen(true);
  };

  const closeEditModal = () => {
    setIsEditModalOpen(false);
    setEditingItem(null);
    setEditFormData({
      type: "",
      questions: [],
      dummy_data: "",
      dummy_metadata: { category: "", type: "" },
    });
    setEditCurrentQuestion("");
  };

  const openViewModal = (item) => {
    setViewingItem(item);
    setIsViewModalOpen(true);
  };

  const closeViewModal = () => {
    setIsViewModalOpen(false);
    setViewingItem(null);
  };

  // Fetch data
  useEffect(() => {
    fetchSetupQuestions();
  }, []);

  const fetchSetupQuestions = async () => {
    try {
      setLoading(true);
      const response = await baseHttp.get("/eko-info/setup-questions");
      setQuestions(response.data.data || []);
      // Initialize all cards as expanded
      const expanded = {};
      response.data.data.forEach((_, index) => {
        expanded[index] = true;
      });
      setExpandedCards(expanded);
    } catch (error) {
      toast.error("Failed to fetch setup questions");
    } finally {
      setLoading(false);
    }
  };

  // Edit modal question management
  const handleAddEditQuestion = () => {
    if (!editCurrentQuestion.trim()) return;
    setEditFormData((prev) => ({
      ...prev,
      questions: [...prev.questions, editCurrentQuestion.trim()],
    }));
    setEditCurrentQuestion("");
  };

  const handleRemoveEditQuestion = (index) => {
    setEditFormData((prev) => ({
      ...prev,
      questions: prev.questions.filter((_, i) => i !== index),
    }));
  };

  // CRUD operations
  const handleAddQuestion = () => {
    if (!currentQuestion.trim()) return;
    setNewQuestion((prev) => ({
      ...prev,
      questions: [...prev.questions, currentQuestion.trim()],
    }));
    setCurrentQuestion("");
  };

  const handleCreate = async () => {
    try {
      await baseHttp.post("/setup-questions", newQuestion);
      toast.success("Question set created successfully");
      fetchSetupQuestions();
      setIsCreating(false);
      resetForm();
    } catch (error) {
      toast.error("Failed to create question set");
    }
  };

  const handleUpdate = async (question) => {
    try {
      await baseHttp.put("/setup-questions", question);
      toast.success("Question set updated successfully");
      setEditingId(null);
      fetchSetupQuestions();
    } catch (error) {
      toast.error("Failed to update question set");
    }
  };

  const handleModalUpdate = async () => {
    try {
      if (!editFormData.type.trim()) {
        toast.error("Type is required");
        return;
      }

      const updatedData = {
        ...editFormData,
        type: editFormData.type.trim(),
      };

      await baseHttp.put("/setup-questions", updatedData);
      toast.success("Question set updated successfully");
      closeEditModal();
      fetchSetupQuestions();
    } catch (error) {
      toast.error("Failed to update question set");
    }
  };

  const handleDelete = async (type) => {
    try {
      await baseHttp.delete("/setup-questions");
      toast.success("Question set deleted successfully");
      fetchSetupQuestions();
    } catch (error) {
      toast.error("Failed to delete question set");
    }
  };

  const resetForm = () => {
    setNewQuestion({
      type: "",
      questions: [],
      dummy_data: "",
      dummy_metadata: { category: "", type: "" },
    });
  };

  return (
    <div className="space-y-8">
      {/* Modern Header */}
      <div className="relative">
        <div className="absolute inset-0 bg-gradient-to-r from-primary/5 to-blue-500/5 rounded-2xl blur-3xl"></div>
        <Card className="relative border-0 bg-gradient-to-r from-card to-card/50 backdrop-blur-sm">
          <CardContent className="p-8">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="relative">
                  <div className="absolute inset-0 bg-gradient-to-r from-primary to-blue-600 rounded-xl blur-lg opacity-20 animate-pulse"></div>
                  <div className="relative bg-gradient-to-r from-primary to-blue-600 p-3 rounded-xl">
                    <FileText className="h-6 w-6 text-white" />
                  </div>
                </div>
                <div>
                  <h2 className="text-3xl font-bold bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent">
                    Setup Questions
                  </h2>
                  <p className="text-muted-foreground mt-1 flex items-center gap-2">
                    <Database className="h-4 w-4" />
                    Manage question sets for different organization types
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <Badge variant="secondary" className="px-3 py-1">
                  <Sparkles className="w-3 h-3 mr-1" />
                  {questions.length} Sets
                </Badge>
                <Button
                  onClick={() => setIsCreating(!isCreating)}
                  className="bg-gradient-to-r from-primary to-blue-600 hover:from-primary/90 hover:to-blue-600/90 text-white shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  {isCreating ? "Cancel" : "Add Question Set"}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Modern Creation Card */}
      {isCreating && (
        <Card className="border-0 shadow-2xl bg-gradient-to-br from-card to-card/50 backdrop-blur-sm animate-in fade-in-50 duration-500">
          <CardHeader className="pb-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-primary/10 rounded-lg">
                <Settings className="h-5 w-5 text-primary" />
              </div>
              <div>
                <CardTitle className="text-xl font-semibold">Create New Question Set</CardTitle>
                <CardDescription>Configure questions and dummy data for organization setup</CardDescription>
              </div>
            </div>
          </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">
                    Type
                  </label>
                  <Input
                    value={newQuestion.type}
                    onChange={(e) =>
                      setNewQuestion({
                        ...newQuestion,
                        type: e.target.value,
                        dummy_metadata: {
                          ...newQuestion.dummy_metadata,
                          type: e.target.value,
                        },
                      })
                    }
                    placeholder="e.g., marketing_sales"
                    className="bg-white"
                  />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">
                    Category
                  </label>
                  <Input
                    value={newQuestion.dummy_metadata.category}
                    onChange={(e) =>
                      setNewQuestion({
                        ...newQuestion,
                        dummy_metadata: {
                          ...newQuestion.dummy_metadata,
                          category: e.target.value,
                        },
                      })
                    }
                    placeholder="e.g., Apple Products"
                    className="bg-white"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">
                  Dummy Data
                </label>
                <Textarea
                  value={newQuestion.dummy_data}
                  onChange={(e) =>
                    setNewQuestion({
                      ...newQuestion,
                      dummy_data: e.target.value,
                    })
                  }
                  placeholder="Enter detailed dummy data..."
                  rows={4}
                  className="bg-white"
                />
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">
                  Questions
                </label>
                <div className="flex gap-2">
                  <Input
                    value={currentQuestion}
                    onChange={(e) => setCurrentQuestion(e.target.value)}
                    placeholder="Enter a question"
                    className="bg-white flex-1"
                    onKeyDown={(e) => e.key === "Enter" && handleAddQuestion()}
                  />
                  <Button
                    onClick={handleAddQuestion}
                    disabled={!currentQuestion.trim()}
                    variant="outline"
                  >
                    <PlusCircle className="h-4 w-4" />
                  </Button>
                </div>

                {newQuestion.questions.length > 0 && (
                  <div className="mt-3 space-y-2 max-h-60 overflow-y-auto p-2 border rounded-lg bg-white">
                    {newQuestion.questions.map((q, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between p-2 hover:bg-gray-50 rounded"
                      >
                        <span className="text-sm">{q}</span>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() =>
                            setNewQuestion({
                              ...newQuestion,
                              questions: newQuestion.questions.filter(
                                (_, i) => i !== index
                              ),
                            })
                          }
                          className="text-red-500 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </CardContent>
            <CardFooter className="flex justify-end gap-2 border-t px-6 py-4">
              <Button variant="outline" onClick={() => setIsCreating(false)}>
                Cancel
              </Button>
              <Button onClick={handleCreate}>Create Set</Button>
            </CardFooter>
          </Card>
        )}

      {/* Modern Question Sets List */}
      {loading ? (
        <div className="flex flex-col items-center justify-center py-16">
          <div className="relative">
            <div className="absolute inset-0 bg-primary/20 rounded-full animate-ping"></div>
            <div className="relative bg-primary/10 p-4 rounded-full">
              <Settings className="h-8 w-8 text-primary animate-spin" />
            </div>
          </div>
          <p className="text-muted-foreground mt-4">Loading question sets...</p>
        </div>
      ) : questions.length === 0 ? (
        <Card className="border-0 bg-gradient-to-br from-muted/30 to-muted/10 backdrop-blur-sm">
          <CardContent className="text-center py-16">
            <div className="mx-auto w-16 h-16 bg-muted rounded-full flex items-center justify-center mb-4">
              <FileText className="h-8 w-8 text-muted-foreground" />
            </div>
            <h3 className="text-lg font-semibold text-foreground mb-2">No Question Sets Found</h3>
            <p className="text-muted-foreground mb-6">Get started by creating your first question set</p>
            <Button
              onClick={() => setIsCreating(true)}
              className="bg-primary hover:bg-primary/90"
            >
              <Plus className="h-4 w-4 mr-2" />
              Create Question Set
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-6">
          {questions.map((item, index) => (
            <Card
              key={index}
              className="group border-0 shadow-lg hover:shadow-2xl transition-all duration-300 bg-gradient-to-br from-card to-card/50 backdrop-blur-sm overflow-hidden"
            >
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-4">
                    <div className="p-2 bg-primary/10 rounded-lg group-hover:bg-primary/20 transition-colors">
                      <FileText className="h-5 w-5 text-primary" />
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold text-foreground">{item.type}</h3>
                      <div className="flex items-center gap-3 mt-1">
                        <Badge variant="secondary" className="text-xs">
                          <Database className="w-3 h-3 mr-1" />
                          {item.questions.length} questions
                        </Badge>
                        <Badge variant="outline" className="text-xs">
                          {item.dummy_metadata.category}
                        </Badge>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => openViewModal(item)}
                      className="opacity-0 group-hover:opacity-100 transition-opacity"
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => openEditModal(item, index)}
                      className="opacity-0 group-hover:opacity-100 transition-opacity"
                    >
                      <Edit2 className="h-4 w-4" />
                    </Button>
                    <Popconfirm
                      title="Delete Question Set"
                      description="Are you sure you want to delete this question set? This action cannot be undone."
                      onConfirm={() => handleDelete(item.type)}
                      okText="Delete"
                      cancelText="Cancel"
                    >
                      <Button
                        variant="ghost"
                        size="sm"
                        className="opacity-0 group-hover:opacity-100 transition-opacity text-destructive hover:text-destructive"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </Popconfirm>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => toggleCard(index)}
                    >
                      {expandedCards[index] ? (
                        <ChevronUp className="h-4 w-4" />
                      ) : (
                        <ChevronDown className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </div>

                {expandedCards[index] && (
                  <div className="border-t border-border/50 mt-4 pt-4">
                    <div className="grid md:grid-cols-2 gap-6">
                      <div className="space-y-3">
                        <h4 className="text-sm font-medium text-muted-foreground flex items-center gap-2">
                          <FileText className="h-4 w-4" />
                          Questions Preview
                        </h4>
                        <div className="space-y-2 max-h-32 overflow-y-auto">
                          {item.questions.slice(0, 3).map((q, qIndex) => (
                            <div
                              key={qIndex}
                              className="text-sm p-2 bg-muted/50 rounded-lg"
                            >
                              {q}
                            </div>
                          ))}
                          {item.questions.length > 3 && (
                            <div className="text-xs text-muted-foreground text-center py-1">
                              +{item.questions.length - 3} more questions
                            </div>
                          )}
                        </div>
                      </div>
                      <div className="space-y-3">
                        <h4 className="text-sm font-medium text-muted-foreground flex items-center gap-2">
                          <Database className="h-4 w-4" />
                          Dummy Data Preview
                        </h4>
                        <div className="text-sm p-3 bg-muted/50 rounded-lg max-h-32 overflow-y-auto">
                          {item.dummy_data.length > 100
                            ? `${item.dummy_data.substring(0, 100)}...`
                            : item.dummy_data}
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </Card>
            ))}
          </div>
        )}
      </div>

      {/* Edit Modal */}
      <Dialog open={isEditModalOpen} onOpenChange={closeEditModal}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Edit2 className="h-5 w-5" />
              Edit Question Set
            </DialogTitle>
            <DialogDescription>
              Modify the question set configuration and dummy data
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-6 py-4">
            <div className="grid md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-type">Type</Label>
                <Input
                  id="edit-type"
                  value={editFormData.type}
                  onChange={(e) =>
                    setEditFormData({
                      ...editFormData,
                      type: e.target.value,
                      dummy_metadata: {
                        ...editFormData.dummy_metadata,
                        type: e.target.value,
                      },
                    })
                  }
                  placeholder="e.g., marketing_sales"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-category">Category</Label>
                <Input
                  id="edit-category"
                  value={editFormData.dummy_metadata.category}
                  onChange={(e) =>
                    setEditFormData({
                      ...editFormData,
                      dummy_metadata: {
                        ...editFormData.dummy_metadata,
                        category: e.target.value,
                      },
                    })
                  }
                  placeholder="e.g., Apple Products"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-dummy-data">Dummy Data</Label>
              <Textarea
                id="edit-dummy-data"
                value={editFormData.dummy_data}
                onChange={(e) =>
                  setEditFormData({
                    ...editFormData,
                    dummy_data: e.target.value,
                  })
                }
                placeholder="Enter detailed dummy data..."
                rows={6}
              />
            </div>

            <div className="space-y-2">
              <Label>Questions</Label>
              <div className="flex gap-2">
                <Input
                  value={editCurrentQuestion}
                  onChange={(e) => setEditCurrentQuestion(e.target.value)}
                  placeholder="Enter a question"
                  className="flex-1"
                  onKeyDown={(e) => e.key === "Enter" && handleAddEditQuestion()}
                />
                <Button
                  onClick={handleAddEditQuestion}
                  disabled={!editCurrentQuestion.trim()}
                  variant="outline"
                  size="sm"
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </div>

              {editFormData.questions.length > 0 && (
                <div className="mt-3 space-y-2 max-h-60 overflow-y-auto p-3 border rounded-lg bg-muted/20">
                  {editFormData.questions.map((q, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-2 hover:bg-muted/50 rounded transition-colors"
                    >
                      <span className="text-sm flex-1">{q}</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleRemoveEditQuestion(index)}
                        className="text-destructive hover:text-destructive"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={closeEditModal}>
              Cancel
            </Button>
            <Button onClick={handleModalUpdate}>
              <Save className="h-4 w-4 mr-2" />
              Save Changes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* View Modal */}
      <Dialog open={isViewModalOpen} onOpenChange={closeViewModal}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Eye className="h-5 w-5" />
              View Question Set: {viewingItem?.type}
            </DialogTitle>
            <DialogDescription>
              Complete details of the question set configuration
            </DialogDescription>
          </DialogHeader>

          {viewingItem && (
            <div className="space-y-6 py-4">
              <div className="grid md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium text-foreground mb-2 flex items-center gap-2">
                      <FileText className="h-4 w-4" />
                      Questions ({viewingItem.questions.length})
                    </h4>
                    <div className="space-y-2 max-h-60 overflow-y-auto">
                      {viewingItem.questions.map((q, index) => (
                        <div
                          key={index}
                          className="p-3 bg-muted/50 rounded-lg text-sm"
                        >
                          <span className="text-xs text-muted-foreground mr-2">
                            {index + 1}.
                          </span>
                          {q}
                        </div>
                      ))}
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium text-foreground mb-2 flex items-center gap-2">
                      <Settings className="h-4 w-4" />
                      Metadata
                    </h4>
                    <div className="space-y-2">
                      <div className="flex justify-between p-2 bg-muted/30 rounded">
                        <span className="text-sm text-muted-foreground">Type:</span>
                        <span className="text-sm font-medium">{viewingItem.dummy_metadata.type}</span>
                      </div>
                      <div className="flex justify-between p-2 bg-muted/30 rounded">
                        <span className="text-sm text-muted-foreground">Category:</span>
                        <span className="text-sm font-medium">{viewingItem.dummy_metadata.category}</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-medium text-foreground mb-2 flex items-center gap-2">
                    <Database className="h-4 w-4" />
                    Dummy Data
                  </h4>
                  <div className="p-4 bg-muted/50 rounded-lg text-sm whitespace-pre-line max-h-80 overflow-y-auto">
                    {viewingItem.dummy_data}
                  </div>
                </div>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={closeViewModal}>
              Close
            </Button>
            <Button onClick={() => {
              closeViewModal();
              openEditModal(viewingItem, viewingItem.index);
            }}>
              <Edit2 className="h-4 w-4 mr-2" />
              Edit
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default SetupQuestions;