import { useState, useEffect } from "react";
import baseHttp from "../../../services/http/base.http";
import OrgTypesView from "./OrgType";
import LanguagesView from "./LanguagesView";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Loader2,
  Settings,
  Globe,
  Building2,
  Sparkles,
  ChevronRight,
  Activity,
  Zap,
  BarChart3,
  Users,
  Shield,
  Cog
} from "lucide-react";
import SetupQuestions from "./SetupQuestions";

const SetupSettingsPage = () => {
  const [orgTypes, setOrgTypes] = useState([]);
  const [languages, setLanguages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState("overview");
  const [isTransitioning, setIsTransitioning] = useState(false);

  useEffect(() => {
    fetchData();
  }, []);

  const handleTabChange = (newTab) => {
    if (newTab === activeTab) return;
    setIsTransitioning(true);
    setTimeout(() => {
      setActiveTab(newTab);
      setIsTransitioning(false);
    }, 150);
  };

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);
      const [orgTypesResponse, languagesResponse] = await Promise.all([
        baseHttp.get("/eko-info/org-type"),
        baseHttp.get("/eko-info/default-language"),
      ]);

      setOrgTypes(orgTypesResponse.data.types || []);
      setLanguages(languagesResponse.data.language || []);
    } catch (err) {
      console.error("Failed to fetch data:", err);
      setError(
        err.response?.data?.message || "Failed to fetch configuration data"
      );
    } finally {
      setLoading(false);
    }
  };

  const handleAddLanguage = async (language) => {
    try {
      setError(null);
      const response = await baseHttp.post("/eko-info/default-language", {
        language: language,
      });
      setLanguages(response.data.language || []);
      return true;
    } catch (err) {
      console.error("Failed to add language:", err);
      setError(err.response?.data?.message || "Failed to add language");
      throw err;
    }
  };

  const handleDeleteLanguage = async (language) => {
    try {
      setError(null);
      const response = await baseHttp.delete(
        `/eko-info/default-language/${encodeURIComponent(language)}`
      );
      setLanguages(response.data.language || []);
      return true;
    } catch (err) {
      console.error("Failed to delete language:", err);
      setError(err.response?.data?.message || "Failed to delete language");
      throw err;
    }
  };

  const handleAddOrgType = async (orgType) => {
    try {
      setError(null);
      const response = await baseHttp.post("/eko-info/org-type", orgType);
      setOrgTypes(response.data.types || []);
      return true;
    } catch (err) {
      console.error("Failed to add organization type:", err);
      setError(
        err.response?.data?.message || "Failed to add organization type"
      );
      throw err;
    }
  };

  const handleUpdateOrgType = async (orgType) => {
    try {
      setError(null);
      const response = await baseHttp.put(
        `/eko-info/org-type/${encodeURIComponent(orgType.name)}`,
        orgType
      );
      setOrgTypes(response.data.types || []);
      return true;
    } catch (err) {
      console.error("Failed to update organization type:", err);
      setError(
        err.response?.data?.message || "Failed to update organization type"
      );
      throw err;
    }
  };

  const handleDeleteOrgType = async (orgTypeName) => {
    try {
      setError(null);
      const response = await baseHttp.delete(
        `/eko-info/org-type/${encodeURIComponent(orgTypeName)}`
      );
      setOrgTypes(response.data.types || []);
      return true;
    } catch (err) {
      console.error("Failed to delete organization type:", err);
      setError(
        err.response?.data?.message || "Failed to delete organization type"
      );
      throw err;
    }
  };

  const handleAddGoal = async (orgTypeName, goal) => {
    try {
      setError(null);
      const orgType = orgTypes.find((ot) => ot.name === orgTypeName);
      if (!orgType) throw new Error("Organization type not found");

      const updatedOrgType = {
        ...orgType,
        agent_goals: [...orgType.agent_goals, goal],
      };

      const response = await baseHttp.put(
        `/eko-info/org-type/${encodeURIComponent(orgTypeName)}`,
        updatedOrgType
      );
      setOrgTypes(response.data.types || []);
      return true;
    } catch (err) {
      console.error("Failed to add goal:", err);
      setError(err.response?.data?.message || "Failed to add goal");
      throw err;
    }
  };

  const handleUpdateGoal = async (orgTypeName, goal) => {
    try {
      setError(null);
      const orgType = orgTypes.find((ot) => ot.name === orgTypeName);
      if (!orgType) throw new Error("Organization type not found");

      const goalIndex = orgType.agent_goals.findIndex(
        (g) => g.prompt_type === goal.prompt_type
      );
      if (goalIndex === -1) throw new Error("Goal not found");

      const response = await baseHttp.put(
        `/eko-info/org-type/${encodeURIComponent(
          orgTypeName
        )}/goal/${goalIndex}`,
        goal
      );
      setOrgTypes(response.data.types || []);
      return true;
    } catch (err) {
      console.error("Failed to update goal:", err);
      setError(err.response?.data?.message || "Failed to update goal");
      throw err;
    }
  };

  const handleDeleteGoal = async (orgTypeName, promptType) => {
    try {
      setError(null);
      const orgType = orgTypes.find((ot) => ot.name === orgTypeName);
      if (!orgType) throw new Error("Organization type not found");

      const goalIndex = orgType.agent_goals.findIndex(
        (g) => g.prompt_type === promptType
      );
      if (goalIndex === -1) throw new Error("Goal not found");

      const response = await baseHttp.delete(
        `/eko-info/org-type/${encodeURIComponent(
          orgTypeName
        )}/goal/${goalIndex}`
      );
      setOrgTypes(response.data.types || []);
      return true;
    } catch (err) {
      console.error("Failed to delete goal:", err);
      setError(err.response?.data?.message || "Failed to delete goal");
      throw err;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20 flex items-center justify-center">
        <div className="relative">
          {/* Animated background circles */}
          <div className="absolute inset-0 -m-8">
            <div className="absolute top-0 left-0 w-16 h-16 bg-primary/10 rounded-full animate-pulse"></div>
            <div className="absolute top-8 right-0 w-12 h-12 bg-blue-500/10 rounded-full animate-pulse delay-300"></div>
            <div className="absolute bottom-0 left-8 w-8 h-8 bg-purple-500/10 rounded-full animate-pulse delay-700"></div>
          </div>

          <Card className="w-96 backdrop-blur-sm border-0 shadow-2xl bg-card/80">
            <CardContent className="flex flex-col items-center justify-center p-12">
              <div className="relative mb-6">
                <div className="absolute inset-0 bg-primary/20 rounded-full animate-ping"></div>
                <div className="relative bg-primary/10 p-4 rounded-full">
                  <Settings className="h-8 w-8 text-primary animate-spin" />
                </div>
              </div>
              <div className="text-center space-y-2">
                <h3 className="text-lg font-semibold text-foreground">Loading Configuration</h3>
                <p className="text-sm text-muted-foreground">Setting up your workspace...</p>
              </div>
              <div className="mt-6 flex space-x-1">
                <div className="w-2 h-2 bg-primary rounded-full animate-bounce"></div>
                <div className="w-2 h-2 bg-primary rounded-full animate-bounce delay-100"></div>
                <div className="w-2 h-2 bg-primary rounded-full animate-bounce delay-200"></div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background via-background to-destructive/5 flex items-center justify-center">
        <Card className="w-96 border-destructive/20 shadow-2xl">
          <CardContent className="flex flex-col items-center justify-center p-8">
            <div className="relative mb-6">
              <div className="absolute inset-0 bg-destructive/20 rounded-full animate-pulse"></div>
              <div className="relative bg-destructive/10 p-4 rounded-full">
                <Zap className="h-8 w-8 text-destructive" />
              </div>
            </div>
            <div className="text-center space-y-3 mb-6">
              <h3 className="text-lg font-semibold text-foreground">Connection Error</h3>
              <p className="text-sm text-destructive">{error}</p>
              <p className="text-xs text-muted-foreground">Please check your connection and try again</p>
            </div>
            <Button
              onClick={fetchData}
              variant="default"
              className="w-full bg-primary hover:bg-primary/90"
            >
              <Activity className="w-4 h-4 mr-2" />
              Retry Connection
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  const tabs = [
    {
      id: "overview",
      label: "Overview",
      icon: BarChart3,
      description: "System overview and statistics"
    },
    {
      id: "org-types",
      label: "Organization Types",
      icon: Building2,
      description: "Manage organization types and goals"
    },
    {
      id: "languages",
      label: "Languages",
      icon: Globe,
      description: "Configure supported languages"
    },
    {
      id: "questions",
      label: "Setup Questions",
      icon: Cog,
      description: "Manage setup questionnaires"
    }
  ];

  const stats = [
    {
      label: "Organization Types",
      value: orgTypes.length,
      icon: Building2,
      color: "text-blue-600",
      bgColor: "bg-blue-50",
      change: "+2 this month"
    },
    {
      label: "Languages",
      value: languages.length,
      icon: Globe,
      color: "text-green-600",
      bgColor: "bg-green-50",
      change: "+1 this week"
    },
    {
      label: "Active Goals",
      value: orgTypes.reduce((acc, org) => acc + (org.agent_goals?.length || 0), 0),
      icon: Sparkles,
      color: "text-purple-600",
      bgColor: "bg-purple-50",
      change: "+5 this month"
    },
    {
      label: "System Health",
      value: "98.5%",
      icon: Shield,
      color: "text-emerald-600",
      bgColor: "bg-emerald-50",
      change: "Excellent"
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/30">
      {/* Modern Header with Glassmorphism */}
      <div className="sticky top-0 z-50 backdrop-blur-xl bg-background/80 border-b border-border/50">
        <div className="max-w-7xl mx-auto px-6 py-6">
          {/* Header Title Section */}
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center gap-4">
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-primary to-blue-600 rounded-2xl blur-lg opacity-20 animate-pulse"></div>
                <div className="relative bg-gradient-to-r from-primary to-blue-600 p-3 rounded-2xl">
                  <Settings className="h-8 w-8 text-white" />
                </div>
              </div>
              <div>
                <h1 className="text-3xl font-bold bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent">
                  Setup Settings
                </h1>
                <p className="text-muted-foreground mt-1">
                  Configure and manage your system settings with ease
                </p>
              </div>
            </div>
            <Badge variant="secondary" className="px-3 py-1 text-sm">
              <Activity className="w-3 h-3 mr-1" />
              Live
            </Badge>
          </div>

          {/* Modern Navigation Tabs */}
          <div className="flex space-x-2 bg-muted/50 p-2 rounded-2xl backdrop-blur-sm border border-border/50">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => handleTabChange(tab.id)}
                  className={`group relative flex items-center gap-3 px-6 py-3 rounded-xl text-sm font-medium transition-all duration-300 ${
                    activeTab === tab.id
                      ? "bg-background text-foreground shadow-lg shadow-primary/10 scale-105"
                      : "text-muted-foreground hover:text-foreground hover:bg-background/50"
                  }`}
                >
                  <Icon className={`h-4 w-4 transition-transform duration-300 ${
                    activeTab === tab.id ? "scale-110" : "group-hover:scale-105"
                  }`} />
                  <span className="hidden sm:inline">{tab.label}</span>
                  {activeTab === tab.id && (
                    <ChevronRight className="h-3 w-3 opacity-60" />
                  )}
                </button>
              );
            })}
          </div>
        </div>
      </div>

      {/* Main Content with Smooth Transitions */}
      <div className="max-w-7xl mx-auto px-6 py-8">
        <div className={`transition-all duration-300 ${
          isTransitioning ? "opacity-0 translate-y-4" : "opacity-100 translate-y-0"
        }`}>

          {/* Overview Tab */}
          {activeTab === "overview" && (
            <div className="space-y-8 animate-in fade-in-50 duration-500">
              {/* Stats Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {stats.map((stat, index) => {
                  const Icon = stat.icon;
                  return (
                    <Card
                      key={stat.label}
                      className="group hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-card to-card/50 backdrop-blur-sm"
                      style={{ animationDelay: `${index * 100}ms` }}
                    >
                      <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm font-medium text-muted-foreground">{stat.label}</p>
                            <p className="text-2xl font-bold text-foreground mt-1">{stat.value}</p>
                            <p className="text-xs text-muted-foreground mt-1">{stat.change}</p>
                          </div>
                          <div className={`${stat.bgColor} p-3 rounded-xl group-hover:scale-110 transition-transform duration-300`}>
                            <Icon className={`h-6 w-6 ${stat.color}`} />
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>

              {/* Quick Actions */}
              <Card className="border-0 bg-gradient-to-r from-card to-card/50 backdrop-blur-sm">
                <CardContent className="p-8">
                  <h3 className="text-xl font-semibold mb-6 flex items-center gap-2">
                    <Zap className="h-5 w-5 text-primary" />
                    Quick Actions
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Button
                      onClick={() => handleTabChange("org-types")}
                      variant="outline"
                      className="h-auto p-4 flex flex-col items-start gap-2 hover:bg-primary/5 hover:border-primary/20 transition-all duration-300"
                    >
                      <Building2 className="h-5 w-5 text-primary" />
                      <div className="text-left">
                        <div className="font-medium">Manage Organizations</div>
                        <div className="text-xs text-muted-foreground">Add or edit organization types</div>
                      </div>
                    </Button>
                    <Button
                      onClick={() => handleTabChange("languages")}
                      variant="outline"
                      className="h-auto p-4 flex flex-col items-start gap-2 hover:bg-primary/5 hover:border-primary/20 transition-all duration-300"
                    >
                      <Globe className="h-5 w-5 text-primary" />
                      <div className="text-left">
                        <div className="font-medium">Configure Languages</div>
                        <div className="text-xs text-muted-foreground">Set up supported languages</div>
                      </div>
                    </Button>
                    <Button
                      onClick={() => handleTabChange("questions")}
                      variant="outline"
                      className="h-auto p-4 flex flex-col items-start gap-2 hover:bg-primary/5 hover:border-primary/20 transition-all duration-300"
                    >
                      <Cog className="h-5 w-5 text-primary" />
                      <div className="text-left">
                        <div className="font-medium">Setup Questions</div>
                        <div className="text-xs text-muted-foreground">Manage questionnaires</div>
                      </div>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Organization Types Tab */}
          {activeTab === "org-types" && (
            <div className="animate-in fade-in-50 duration-500">
              <OrgTypesView
                orgTypes={orgTypes}
                onAddOrgType={handleAddOrgType}
                onUpdateOrgType={handleUpdateOrgType}
                onDeleteOrgType={handleDeleteOrgType}
                onAddGoal={handleAddGoal}
                onUpdateGoal={handleUpdateGoal}
                onDeleteGoal={handleDeleteGoal}
                error={error}
                setError={setError}
              />
            </div>
          )}

          {/* Languages Tab */}
          {activeTab === "languages" && (
            <div className="animate-in fade-in-50 duration-500">
              <LanguagesView
                languages={languages}
                onAddLanguage={handleAddLanguage}
                onDeleteLanguage={handleDeleteLanguage}
              />
            </div>
          )}

          {/* Setup Questions Tab */}
          {activeTab === "questions" && (
            <div className="animate-in fade-in-50 duration-500">
              <SetupQuestions />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SetupSettingsPage;
