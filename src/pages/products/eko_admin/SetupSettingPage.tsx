import { useState, useEffect } from "react";
import baseHttp from "../../../services/http/base.http";
import OrgTypesView from "./OrgType";
import LanguagesView from "./LanguagesView";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Loader2, Settings, Globe, Building2 } from "lucide-react";
import SetupQuestions from "./SetupQuestions";

const SetupSettingsPage = () => {
  const [orgTypes, setOrgTypes] = useState([]);
  const [languages, setLanguages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState("org-types");

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);
      const [orgTypesResponse, languagesResponse] = await Promise.all([
        baseHttp.get("/eko-info/org-type"),
        baseHttp.get("/eko-info/default-language"),
      ]);

      setOrgTypes(orgTypesResponse.data.types || []);
      setLanguages(languagesResponse.data.language || []);
    } catch (err) {
      console.error("Failed to fetch data:", err);
      setError(
        err.response?.data?.message || "Failed to fetch configuration data"
      );
    } finally {
      setLoading(false);
    }
  };

  const handleAddLanguage = async (language) => {
    try {
      setError(null);
      const response = await baseHttp.post("/eko-info/default-language", {
        language: language,
      });
      setLanguages(response.data.language || []);
      return true;
    } catch (err) {
      console.error("Failed to add language:", err);
      setError(err.response?.data?.message || "Failed to add language");
      throw err;
    }
  };

  const handleDeleteLanguage = async (language) => {
    try {
      setError(null);
      const response = await baseHttp.delete(
        `/eko-info/default-language/${encodeURIComponent(language)}`
      );
      setLanguages(response.data.language || []);
      return true;
    } catch (err) {
      console.error("Failed to delete language:", err);
      setError(err.response?.data?.message || "Failed to delete language");
      throw err;
    }
  };

  const handleAddOrgType = async (orgType) => {
    try {
      setError(null);
      const response = await baseHttp.post("/eko-info/org-type", orgType);
      setOrgTypes(response.data.types || []);
      return true;
    } catch (err) {
      console.error("Failed to add organization type:", err);
      setError(
        err.response?.data?.message || "Failed to add organization type"
      );
      throw err;
    }
  };

  const handleUpdateOrgType = async (orgType) => {
    try {
      setError(null);
      const response = await baseHttp.put(
        `/eko-info/org-type/${encodeURIComponent(orgType.name)}`,
        orgType
      );
      setOrgTypes(response.data.types || []);
      return true;
    } catch (err) {
      console.error("Failed to update organization type:", err);
      setError(
        err.response?.data?.message || "Failed to update organization type"
      );
      throw err;
    }
  };

  const handleDeleteOrgType = async (orgTypeName) => {
    try {
      setError(null);
      const response = await baseHttp.delete(
        `/eko-info/org-type/${encodeURIComponent(orgTypeName)}`
      );
      setOrgTypes(response.data.types || []);
      return true;
    } catch (err) {
      console.error("Failed to delete organization type:", err);
      setError(
        err.response?.data?.message || "Failed to delete organization type"
      );
      throw err;
    }
  };

  const handleAddGoal = async (orgTypeName, goal) => {
    try {
      setError(null);
      const orgType = orgTypes.find((ot) => ot.name === orgTypeName);
      if (!orgType) throw new Error("Organization type not found");

      const updatedOrgType = {
        ...orgType,
        agent_goals: [...orgType.agent_goals, goal],
      };

      const response = await baseHttp.put(
        `/eko-info/org-type/${encodeURIComponent(orgTypeName)}`,
        updatedOrgType
      );
      setOrgTypes(response.data.types || []);
      return true;
    } catch (err) {
      console.error("Failed to add goal:", err);
      setError(err.response?.data?.message || "Failed to add goal");
      throw err;
    }
  };

  const handleUpdateGoal = async (orgTypeName, goal) => {
    try {
      setError(null);
      const orgType = orgTypes.find((ot) => ot.name === orgTypeName);
      if (!orgType) throw new Error("Organization type not found");

      const goalIndex = orgType.agent_goals.findIndex(
        (g) => g.prompt_type === goal.prompt_type
      );
      if (goalIndex === -1) throw new Error("Goal not found");

      const response = await baseHttp.put(
        `/eko-info/org-type/${encodeURIComponent(
          orgTypeName
        )}/goal/${goalIndex}`,
        goal
      );
      setOrgTypes(response.data.types || []);
      return true;
    } catch (err) {
      console.error("Failed to update goal:", err);
      setError(err.response?.data?.message || "Failed to update goal");
      throw err;
    }
  };

  const handleDeleteGoal = async (orgTypeName, promptType) => {
    try {
      setError(null);
      const orgType = orgTypes.find((ot) => ot.name === orgTypeName);
      if (!orgType) throw new Error("Organization type not found");

      const goalIndex = orgType.agent_goals.findIndex(
        (g) => g.prompt_type === promptType
      );
      if (goalIndex === -1) throw new Error("Goal not found");

      const response = await baseHttp.delete(
        `/eko-info/org-type/${encodeURIComponent(
          orgTypeName
        )}/goal/${goalIndex}`
      );
      setOrgTypes(response.data.types || []);
      return true;
    } catch (err) {
      console.error("Failed to delete goal:", err);
      setError(err.response?.data?.message || "Failed to delete goal");
      throw err;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <Card className="w-96">
          <CardContent className="flex flex-col items-center justify-center p-8">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground mb-4" />
            <p className="text-muted-foreground">Loading configuration...</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <Card className="w-96">
          <CardContent className="flex flex-col items-center justify-center p-8">
            <p className="text-destructive mb-4">Error: {error}</p>
            <Button onClick={fetchData} variant="default">
              Retry
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="border-b border-border bg-card">
        <div className="max-w-7xl mx-auto px-6 py-6">
          <div className="flex items-center gap-3 mb-6">
            <div className="p-2 bg-primary/10 rounded-lg">
              <Settings className="h-6 w-6 text-primary" />
            </div>
            <div>
              <h1 className="text-2xl font-semibold text-foreground">Setup Settings</h1>
              <p className="text-muted-foreground">Configure system organization types and languages</p>
            </div>
          </div>

          {/* Navigation Tabs */}
          <div className="flex space-x-1 bg-muted p-1 rounded-lg w-fit">
            <button
              onClick={() => setActiveTab("org-types")}
              className={`flex items-center gap-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                activeTab === "org-types"
                  ? "bg-background text-foreground shadow-sm"
                  : "text-muted-foreground hover:text-foreground"
              }`}
            >
              <Building2 className="h-4 w-4" />
              Organization Types
            </button>
            <button
              onClick={() => setActiveTab("languages")}
              className={`flex items-center gap-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                activeTab === "languages"
                  ? "bg-background text-foreground shadow-sm"
                  : "text-muted-foreground hover:text-foreground"
              }`}
            >
              <Globe className="h-4 w-4" />
              Languages
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-6 py-8">
        {activeTab === "org-types" && (
          <div>
          <OrgTypesView
            orgTypes={orgTypes}
            onAddOrgType={handleAddOrgType}
            onUpdateOrgType={handleUpdateOrgType}
            onDeleteOrgType={handleDeleteOrgType}
            onAddGoal={handleAddGoal}
            onUpdateGoal={handleUpdateGoal}
            onDeleteGoal={handleDeleteGoal}
            error={error}
            setError={setError}
          />
          <SetupQuestions/>
          </div>
        )}
        {activeTab === "languages" && (
          <LanguagesView
            languages={languages}
            onAddLanguage={handleAddLanguage}
            onDeleteLanguage={handleDeleteLanguage}
          />
        )}
      </div>
    </div>
  );
};

export default SetupSettingsPage;
