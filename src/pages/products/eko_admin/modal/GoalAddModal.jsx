import React, { useEffect, useState } from "react";
import { Icon } from "@iconify/react";


  // OrgType Add/Edit Modal
  const OrgTypeModal = ({ isOpen, onClose, orgType, onSave, mode = "add" }) => {
    const [formData, setFormData] = useState({
      name: orgType?.name || "",
      label: orgType?.label || "",
      agent_goals: orgType?.agent_goals || [],
    });
  
    const handleSave = () => {
      if (!formData.name || !formData.label) return;
      onSave(formData);
      onClose();
    };
  
    const handleInputChange = (field, value) => {
      setFormData(prev => ({ ...prev, [field]: value }));
    };
  
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>{mode === "add" ? "Add Organization Type" : "Edit Organization Type"}</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="name">Name</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => handleInputChange("name", e.target.value)}
                placeholder="e.g., marketing_sales"
              />
            </div>
            <div>
              <Label htmlFor="label">Label</Label>
              <Input
                id="label"
                value={formData.label}
                onChange={(e) => handleInputChange("label", e.target.value)}
                placeholder="e.g., Marketing and Sales"
              />
            </div>
            <div className="flex gap-2 pt-4">
              <Button onClick={handleSave} className="flex-1">
                <Save className="h-4 w-4 mr-2" />
                {mode === "add" ? "Add" : "Save"}
              </Button>
              <Button variant="outline" onClick={onClose} className="flex-1">Cancel</Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    );
  };
  

export default OrgTypeModal;
