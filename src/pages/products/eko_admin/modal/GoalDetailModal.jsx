import React from "react";
import {
  Dialog,
  Dialog<PERSON>ontent,
  Di<PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Target, Edit, Trash2 } from "lucide-react";

const GoalDetailModal = ({ isOpen, onClose, goal, orgTypeName, onEdit, onDelete }) => {
  if (!goal) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Goal Details
          </DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div>
            <Label className="text-sm font-medium text-muted-foreground">
              Prompt Type
            </Label>
            <p className="text-sm bg-muted px-3 py-2 rounded-md font-mono">
              {goal.prompt_type}
            </p>
          </div>
          <div>
            <Label className="text-sm font-medium text-muted-foreground">
              Summary
            </Label>
            <p className="text-sm bg-muted px-3 py-2 rounded-md">
              {goal.prompt_summary}
            </p>
          </div>
          <div>
            <Label className="text-sm font-medium text-muted-foreground">
              Prompt
            </Label>
            <div className="text-sm bg-muted px-3 py-2 rounded-md max-h-48 overflow-y-auto">
              {goal.prompt}
            </div>
          </div>
          <div className="flex gap-2 pt-4">
            <Button
              onClick={() => onEdit(goal)}
              variant="outline"
              className="flex-1"
            >
              <Edit className="h-4 w-4 mr-2" /> Edit
            </Button>
            <Button
              onClick={() => onDelete(goal.prompt_type)}
              variant="destructive"
              className="flex-1"
            >
              <Trash2 className="h-4 w-4 mr-2" /> Delete
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default GoalDetailModal;