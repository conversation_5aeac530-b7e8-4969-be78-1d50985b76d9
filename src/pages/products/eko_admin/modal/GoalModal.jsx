import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  Di<PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Save } from "lucide-react";

const GoalModal = ({ isOpen, onClose, goal, orgTypeName, onSave, mode = "add" }) => {
  const [formData, setFormData] = useState({
    prompt_type: "",
    prompt: "",
    prompt_summary: "",
  });

  useEffect(() => {
    if (isOpen) {
      setFormData({
        prompt_type: goal?.prompt_type || "",
        prompt: goal?.prompt || "",
        prompt_summary: goal?.prompt_summary || "",
      });
    }
  }, [goal, isOpen]);

  const handleSave = () => {
    if (!formData.prompt_type || !formData.prompt || !formData.prompt_summary)
      return;
    onSave(orgTypeName, formData);
    onClose();
  };

  const handleInputChange = (field, value) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>{mode === "add" ? "Add Goal" : "Edit Goal"}</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div>
            <Label htmlFor="prompt_type">Prompt Type</Label>
            <Input
              id="prompt_type"
              value={formData.prompt_type}
              onChange={(e) => handleInputChange("prompt_type", e.target.value)}
              placeholder="e.g., sales_1"
            />
          </div>
          <div>
            <Label htmlFor="prompt_summary">Summary</Label>
            <Input
              id="prompt_summary"
              value={formData.prompt_summary}
              onChange={(e) =>
                handleInputChange("prompt_summary", e.target.value)
              }
              placeholder="Brief description of the goal"
            />
          </div>
          <div>
            <Label htmlFor="prompt">Prompt</Label>
            <Textarea
              id="prompt"
              value={formData.prompt}
              onChange={(e) => handleInputChange("prompt", e.target.value)}
              placeholder="Detailed prompt instructions..."
              rows={6}
            />
          </div>
          <div className="flex gap-2 pt-4">
            <Button onClick={handleSave} className="flex-1">
              <Save className="h-4 w-4 mr-2" />
              {mode === "add" ? "Add Goal" : "Save Goal"}
            </Button>
            <Button variant="outline" onClick={onClose} className="flex-1">
              Cancel
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default GoalModal;