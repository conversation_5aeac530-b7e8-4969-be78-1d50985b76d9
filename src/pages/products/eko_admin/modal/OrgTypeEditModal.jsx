import React from "react";
import { Icon } from "@iconify/react";

const OrgTypeEditModal = ({
  orgType,
  onClose,
  onSave,
  onDelete,
  onChange,
  isAdding,
}) => {
  if (!orgType) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white w-full max-w-3xl max-h-[80vh] overflow-y-auto">
        <div className="border-b border-gray-200 p-6">
          <div className="flex justify-between items-start">
            <div>
              <h3 className="text-xl font-bold text-black mb-4">
                {isAdding
                  ? "Add New Organization Type"
                  : "Edit Organization Type"}
              </h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Name (ID)
                  </label>
                  <input
                    type="text"
                    value={orgType.name}
                    onChange={(e) =>
                      onChange({ ...orgType, name: e.target.value })
                    }
                    className="w-full border border-gray-300 p-2"
                    disabled={!isAdding}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Display Label
                  </label>
                  <input
                    type="text"
                    value={orgType.label}
                    onChange={(e) =>
                      onChange({ ...orgType, label: e.target.value })
                    }
                    className="w-full border border-gray-300 p-2"
                  />
                </div>
              </div>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-black transition-colors"
            >
              <Icon icon="solar:close-circle-bold" className="text-2xl" />
            </button>
          </div>
        </div>

        <div className="p-6 border-t border-gray-200 flex justify-end gap-3">
          {!isAdding && (
            <button
              onClick={onDelete}
              className="px-4 py-2 text-red-600 hover:bg-red-50 transition-colors"
            >
              Delete Organization Type
            </button>
          )}
          <button
            onClick={onSave}
            className="px-4 py-2 bg-black text-white hover:bg-gray-800 transition-colors"
          >
            {isAdding ? "Add Organization Type" : "Save Changes"}
          </button>
        </div>
      </div>
    </div>
  );
};

export default OrgTypeEditModal;
