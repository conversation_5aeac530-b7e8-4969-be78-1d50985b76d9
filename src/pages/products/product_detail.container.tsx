import React, { useEffect, useState } from "react";
import ProductsDetails from "./product_detail/products.info";
import baseHttp from "../../services/http/base.http";
import useDebounce from "../../hooks/useDebounce";

const ProductDetailsContainer: React.FC = () => {
  const [tenants, setTenants] = useState<any[]>([]);
  const [total, setTotal] = useState(0);
  const [skip, setSkip] = useState(0);
  const [limit] = useState(12);
  const [search, setSearch] = useState("");
  const debouncedSearch = useDebounce(search);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchTenants = async (newSkip = 0, searchTerm = debouncedSearch) => {
    setLoading(true);
    try {
      const response = await baseHttp.post<{
        tenants: any[];
        total: number;
        skip: number;
        limit: number;
      }>("/product-info/eko_admin/details", {
        skip: newSkip,
        limit,
        search: searchTerm,
      });

      const { tenants, total, skip } = response.data;
      setTenants(tenants || []);
      setTotal(total || 0);
      setSkip(skip || 0);
    } catch (err: any) {
      console.error("Fetch tenants error:", err);
      setError(err.message || "Failed to fetch tenants");
    } finally {
      setLoading(false);
    }
  };

  const handlePageChange = (newSkip: number) => {
    fetchTenants(newSkip);
  };

  const handleSearchChange = (value: string) => {
    setSearch(value);
    setSkip(0); // Reset to first page
  };

  // Refetch when debouncedSearch changes
  useEffect(() => {
    fetchTenants(0, debouncedSearch);
  }, [debouncedSearch]);

  useEffect(() => {
    fetchTenants(0, debouncedSearch);
  }, []);

  return (
    <ProductsDetails
      tenants={tenants}
      total={total}
      skip={skip}
      limit={limit}
      loading={loading}
      error={error}
      search={search}
      onSearchChange={handleSearchChange}
      onPageChange={handlePageChange}
    />
  );
};

export default ProductDetailsContainer;
