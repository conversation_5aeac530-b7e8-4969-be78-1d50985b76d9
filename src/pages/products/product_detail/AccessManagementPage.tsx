import React, { useEffect, useState } from "react";
import baseHttp from "../../../services/http/base.http";
import NavPermission from "./NavigationPermission";
import NavAccess from "./FilterAccess";

export interface AccessData {
  nav_access: Array<{
    name: string;
    admin: any;
    agent: any;
  }>;
  nav_permission: Array<{
    name: string;
    admin: Record<string, boolean>;
    agent: Record<string, boolean>;
    supervisor: Record<string, boolean>;
  }>;
}

const AccessManagementPage: React.FC<{ tenant_db_name: string }> = ({
  tenant_db_name,
}) => {
  const [data, setData] = useState<AccessData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeSection, setActiveSection] = useState<
    "nav_access" | "nav_permission"
  >("nav_access");

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await baseHttp.get("/product-info/tenant-acess", {
          params: { tenant_db_name },
        });
        setData(response.data);
      } catch (err) {
        setError("Failed to fetch access data");
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [tenant_db_name]);

  const handleToggle = (
    role: "admin" | "agent",
    path: string[],
    value: boolean
  ) => {
    if (!data) return;

    const newData = JSON.parse(JSON.stringify(data));
    let current = newData.nav_access[0][role];

    // Navigate to the correct nested object
    for (let i = 0; i < path.length - 1; i++) {
      if (current[path[i]]) {
        current = current[path[i]];
      } else {
        console.error(`Path ${path[i]} not found in`, current);
        return;
      }
    }

    // Set the final value
    current[path[path.length - 1]] = value;
    setData(newData);
  };

  const handleAddItem = (path: string[], newItem: string) => {
    if (!data) return;

    const newData = JSON.parse(JSON.stringify(data));
    const formattedKey = newItem.trim().replace(/\s+/g, "_");

    if (path[0] === "nav_access") {
      // For nav_access, we need to add to both admin and agent
      const accessPath = path.slice(1); // Remove "nav_access" from path

      // Navigate to admin path and add item
      let adminCurrent = newData.nav_access[0].admin;
      for (const key of accessPath) {
        if (!adminCurrent[key]) {
          adminCurrent[key] = {};
        }
        adminCurrent = adminCurrent[key];
      }
      adminCurrent[formattedKey] = true;

      // Navigate to agent path and add item
      let agentCurrent = newData.nav_access[0].agent;
      for (const key of accessPath) {
        if (!agentCurrent[key]) {
          agentCurrent[key] = {};
        }
        agentCurrent = agentCurrent[key];
      }
      agentCurrent[formattedKey] = true;
    } else if (path[0] === "nav_permission") {
      // For nav_permission, add the item as-is (don't format to lowercase with underscores)
      const permissionKey = newItem.trim();
      newData.nav_permission[0].admin[permissionKey] = true;
      newData.nav_permission[0].agent[permissionKey] = false;
      if (newData.nav_permission[0].supervisor) {
        newData.nav_permission[0].supervisor[permissionKey] = false;
      }
    }

    setData(newData);
  };

  const handleRemoveItem = (path: string[], item: string) => {
    if (!data) return;

    const newData = JSON.parse(JSON.stringify(data));

    if (path[0] === "nav_access") {
      // For nav_access, remove from both admin and agent
      const formattedKey = item.trim().replace(/\s+/g, "_");
      const accessPath = path.slice(1); // Remove "nav_access" from path

      // Navigate admin path and remove item
      let adminCurrent = newData.nav_access[0].admin;
      for (const key of accessPath) {
        if (adminCurrent[key]) {
          adminCurrent = adminCurrent[key];
        } else {
          console.error(`Admin path ${key} not found`);
          return;
        }
      }
      if (adminCurrent.hasOwnProperty(formattedKey)) {
        delete adminCurrent[formattedKey];
      }

      // Navigate agent path and remove item
      let agentCurrent = newData.nav_access[0].agent;
      for (const key of accessPath) {
        if (agentCurrent[key]) {
          agentCurrent = agentCurrent[key];
        } else {
          console.error(`Agent path ${key} not found`);
          return;
        }
      }
      if (agentCurrent.hasOwnProperty(formattedKey)) {
        delete agentCurrent[formattedKey];
      }
    } else if (path[0] === "nav_permission") {
      // For nav_permission, remove the item as-is (use the exact key)
      const permissionKey = item;
      if (newData.nav_permission[0].admin?.hasOwnProperty(permissionKey)) {
        delete newData.nav_permission[0].admin[permissionKey];
      }
      if (newData.nav_permission[0].agent?.hasOwnProperty(permissionKey)) {
        delete newData.nav_permission[0].agent[permissionKey];
      }
      if (newData.nav_permission[0].supervisor?.hasOwnProperty(permissionKey)) {
        delete newData.nav_permission[0].supervisor[permissionKey];
      }
    }

    setData(newData);
  };

  const handlePermissionToggle = (
    role: "admin" | "agent",
    permission: string,
    value: boolean
  ) => {
    if (!data) return;

    const newData = JSON.parse(JSON.stringify(data));
    newData.nav_permission[0][role][permission] = value;
    setData(newData);
  };
  useEffect(() => {
    if (!data) return;
    const timeout = setTimeout(() => {
      handleSave();
    }, 300); // debounce to avoid rapid saves

    return () => clearTimeout(timeout);
  }, [data]);

  const handleSave = async () => {
    if (!data) return;

    try {
      const payload = {
        nav_access: [
          {
            name: "nav_access",
            admin: data.nav_access[0].admin,
            agent: data.nav_access[0].agent,
          },
        ],
        nav_permission: [
          {
            name: "nav_permission",
            admin: data.nav_permission[0].admin,
            agent: data.nav_permission[0].agent,
            supervisor: data.nav_permission[0].supervisor || {},
          },
        ],
      };

      await baseHttp.put("/product-info/tenant-acess", payload, {
        params: { tenant_db_name },
      });

      // Optional: Show success message
      console.log("Settings saved successfully!");
    } catch (err) {
      console.error("Failed to save settings:", err);
      throw err; // Re-throw to let the calling component handle the error
    }
  };

  if (loading)
    return <div className="p-4 text-center text-gray-700">Loading...</div>;
  if (error)
    return <div className="p-4 text-center text-gray-700">{error}</div>;
  if (!data)
    return (
      <div className="p-4 text-center text-gray-700">No data available</div>
    );

  return (
    <div className="max-w-7xl mx-auto p-6">
      <div className="bg-white rounded-xl border border-gray-200 shadow-sm p-6">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-semibold text-gray-800">
            Access Management
          </h2>
          {/* <button
            onClick={handleSave}
            className="px-4 py-2 bg-gray-800 text-white rounded-lg hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 text-sm font-medium"
          >
            Save Changes
          </button> */}
        </div>

        {/* Section Tabs */}
        <div className="flex border-b border-gray-200 mb-6">
          <button
            className={`px-4 py-2 font-medium text-sm ${
              activeSection === "nav_access"
                ? "text-gray-800 border-b-2 border-gray-800"
                : "text-gray-500 hover:text-gray-700"
            }`}
            onClick={() => setActiveSection("nav_access")}
          >
            Access Controls
          </button>
          <button
            className={`px-4 py-2 font-medium text-sm ${
              activeSection === "nav_permission"
                ? "text-gray-800 border-b-2 border-gray-800"
                : "text-gray-500 hover:text-gray-700"
            }`}
            onClick={() => setActiveSection("nav_permission")}
          >
            Navigation Permissions
          </button>
        </div>

        {/* Content */}
        {activeSection === "nav_access" ? (
          <NavAccess
            data={data}
            onToggle={handleToggle}
            onAddItem={handleAddItem}
            onRemoveItem={handleRemoveItem}
            onSave={handleSave}
          />
        ) : (
          <NavPermission
            data={data}
            onToggle={handleToggle}
            onAddItem={handleAddItem}
            onRemoveItem={handleRemoveItem}
            onPermissionToggle={handlePermissionToggle}
            onSave={handleSave}
          />
        )}
      </div>
    </div>
  );
};

export default AccessManagementPage;
