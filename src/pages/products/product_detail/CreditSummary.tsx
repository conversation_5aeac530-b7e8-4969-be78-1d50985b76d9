import { Icon } from "@iconify/react";

interface CreditSummaryProps {
  credit_info?: {
    total_credits?: number;
    remaining?: number;
  };
  ai_status?: {
    enabled?: boolean;
  };
}

const CreditSummary = ({ credit_info, ai_status }: CreditSummaryProps) => {
  const calculateUsagePercentage = () => {
    if (!credit_info?.total_credits || !credit_info?.remaining) return 0;
    return (
      ((credit_info.total_credits - credit_info.remaining) /
        credit_info.total_credits) *
      100
    );
  };

  const usagePercentage = calculateUsagePercentage();

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      {/* Total Credits Card */}
      <div className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl p-6 border border-gray-200 hover:shadow-lg transition-all duration-300">
        <div className="flex items-center justify-between mb-4">
          <div className="p-3 bg-gray-700 rounded-xl">
            <Icon icon="solar:dollar-bold" className="text-white text-2xl" />
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold text-gray-800">
              {credit_info?.total_credits?.toLocaleString() ?? "-"}
            </div>
            <div className="text-sm text-gray-600 font-medium">
              Total Credits
            </div>
          </div>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className="bg-gray-700 h-2 rounded-full"
            style={{ width: `${usagePercentage}%` }}
          ></div>
        </div>
      </div>

      {/* Remaining Credits Card */}
      <div className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl p-6 border border-gray-200 hover:shadow-lg transition-all duration-300">
        <div className="flex items-center justify-between mb-4">
          <div className="p-3 bg-gray-700 rounded-xl">
            <Icon icon="solar:wallet-bold" className="text-white text-2xl" />
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold text-gray-800">
              {credit_info?.remaining?.toLocaleString() ?? "-"}
            </div>
            <div className="text-sm text-gray-600 font-medium">
              Remaining Credits
            </div>
          </div>
        </div>
        <div className="text-xs text-gray-700 mt-2">
          {credit_info?.remaining !== undefined &&
          credit_info?.total_credits !== undefined
            ? `${Math.round(100 - usagePercentage)}% remaining`
            : "-"}
        </div>
      </div>

      {/* AI Status Card */}
      <div className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl p-6 border border-gray-200 hover:shadow-lg transition-all duration-300">
        <div className="flex items-center justify-between mb-4">
          <div className="p-3 bg-gray-700 rounded-xl">
            <Icon icon="solar:cpu-bolt-bold" className="text-white text-2xl" />
          </div>
          <div className="text-right">
            <div className="flex items-center gap-2 justify-end">
              <div
                className={`w-3 h-3 rounded-full ${
                  ai_status?.enabled ? "bg-green-600" : "bg-gray-400"
                }`}
              ></div>
              <div className="text-2xl font-bold text-green-800">
                {ai_status?.enabled ? "Active" : "Inactive"}
              </div>
            </div>
            <div className="text-sm text-gray-600 font-medium">AI Status</div>
          </div>
        </div>
        <div className="text-xs text-gray-700">
          {ai_status?.enabled
            ? "AI services are running"
            : "AI services are paused"}
        </div>
      </div>
    </div>
  );
};

export default CreditSummary;
