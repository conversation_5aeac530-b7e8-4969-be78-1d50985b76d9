import React, { useState } from "react";
import { AccessData } from "./AccessManagementPage";
import { Popconfirm } from "@/components/ui/popconfirm";
import { Trash2 } from "lucide-react";

interface NavAccessProps {
  data: AccessData;
  onToggle: (role: "admin" | "agent", path: string[], value: boolean) => void;
  onAddItem: (path: string[], newItem: string) => void;
  onRemoveItem: (path: string[], item: string) => void;
}

const NavAccess: React.FC<NavAccessProps> = ({
  data,
  onToggle,
  onAddItem,
  onRemoveItem,
}) => {
  const [newItems, setNewItems] = useState<Record<string, string>>({});
  const [editingSections, setEditingSections] = useState<
    Record<string, boolean>
  >({});

  const handleAddClick = (sectionPath: string) => {
    setEditingSections((prev) => ({ ...prev, [sectionPath]: true }));
    setNewItems((prev) => ({ ...prev, [sectionPath]: "" }));
  };

  const handleNewItemChange = (sectionPath: string, value: string) => {
    setNewItems((prev) => ({ ...prev, [sectionPath]: value }));
  };

  const handleSaveNewItem = (path: string[]) => {
    const sectionPath = path.join("-");
    const newItem = newItems[sectionPath];
    if (newItem.trim()) {
      // Add "nav_access" prefix to the path for the main handler
      onAddItem(["nav_access", ...path], newItem);
      setEditingSections((prev) => ({ ...prev, [sectionPath]: false }));
      setNewItems((prev) => ({ ...prev, [sectionPath]: "" }));
    }
  };

  const handleRemoveItem = (path: string[], item: string) => {
    // Add "nav_access" prefix to the path for the main handler
    onRemoveItem(["nav_access", ...path], item);
  };

  const renderSection = (sectionName: string, path: string[]) => {
    const sectionPath = path.join("-");
    const isEditing = editingSections[sectionPath];

    // Get the section data from admin
    let sectionData = data.nav_access[0].admin;
    for (const key of path) {
      if (sectionData && sectionData[key]) {
        sectionData = sectionData[key];
      } else {
        console.error(`Section path ${key} not found in admin data`);
        return null;
      }
    }

    // Get agent data for the same path
    let agentData = data.nav_access[0].agent;
    for (const key of path) {
      if (agentData && agentData[key]) {
        agentData = agentData[key];
      } else {
        console.error(`Section path ${key} not found in agent data`);
        return null;
      }
    }

    return (
      <div key={sectionName} className="space-y-3">
        <div className="flex justify-between items-center mr-5">
          <h4 className="text-sm font-medium text-gray-700 uppercase tracking-wider">
            {sectionName.replace("_", " ")}
          </h4>
          <div className="grid grid-cols-2 gap-2 mb-2">
            <span className="text-xs text-gray-500 text-center">Admin</span>
            <span className="text-xs text-gray-500 text-center">Agent</span>
          </div>
        </div>
        <div className="space-y-2">
          {Object.entries(sectionData).map(([key]) => (
            <div
              key={key}
              className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
            >
              <span className="text-sm text-gray-700">
                {key.replace("_", " ")}
              </span>
              <div className="flex items-center space-x-3">
                <ToggleSwitch
                  checked={sectionData[key]}
                  onChange={(checked) =>
                    onToggle("admin", [...path, key], checked)
                  }
                />
                <ToggleSwitch
                  checked={agentData[key]}
                  onChange={(checked) =>
                    onToggle("agent", [...path, key], checked)
                  }
                />
                <Popconfirm
                  title="Are you sure to delete this item?"
                  onConfirm={() => handleRemoveItem(path, key)}
                  okText="Yes"
                  cancelText="No"
                >
                  <button
                    className="text-red-500 hover:text-red-700"
                    title="Delete"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                </Popconfirm>
              </div>
            </div>
          ))}

          {isEditing ? (
            <div className="flex items-center gap-2 p-2">
              <input
                type="text"
                value={newItems[sectionPath] || ""}
                onChange={(e) =>
                  handleNewItemChange(sectionPath, e.target.value)
                }
                className="flex-1 px-2 py-1 text-sm border border-gray-300 rounded"
                placeholder="Enter new item name"
              />
              <button
                onClick={() => handleSaveNewItem(path)}
                className="px-2 py-1 bg-blue-500 text-white text-sm rounded hover:bg-blue-600"
              >
                Add
              </button>
              <button
                onClick={() =>
                  setEditingSections((prev) => ({
                    ...prev,
                    [sectionPath]: false,
                  }))
                }
                className="px-2 py-1 bg-gray-300 text-gray-700 text-sm rounded hover:bg-gray-400"
              >
                Cancel
              </button>
            </div>
          ) : (
            <button
              onClick={() => handleAddClick(sectionPath)}
              className="w-full flex items-center justify-center gap-1 p-2 text-sm text-blue-500 hover:text-blue-700 hover:bg-blue-50 rounded"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 4v16m8-8H4"
                />
              </svg>
              Add Item
            </button>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* CTA Section */}
      <div className="bg-white p-6 rounded-xl border border-gray-200 shadow-xs">
        <h3 className="font-medium text-gray-800 mb-4 text-lg">CTA Access</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {["channel", "type", "status"].map((section) =>
            renderSection(section, ["CTA", section])
          )}
        </div>
      </div>

      {/* AI Response Section */}
      <div className="bg-white p-6 rounded-xl border border-gray-200 shadow-xs">
        <h3 className="font-medium text-gray-800 mb-4 text-lg">
          AI Response Access
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {["channel", "status", "CTA_type", "ai_replied"].map((section) =>
            renderSection(section, ["ai_response", section])
          )}
        </div>
      </div>

      {/* Settings Section */}
      <div className="bg-white p-6 rounded-xl border border-gray-200 shadow-xs">
        <h3 className="font-medium text-gray-800 mb-4 text-lg">
          Settings Menu
        </h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          <div className="col-span-full flex justify-end pr-4">
            <div className="flex space-x-6">
              <span className="text-xs text-gray-500">Admin</span>
              <span className="text-xs text-gray-500">Agent</span>
            </div>
          </div>
          {Object.entries(
            data.nav_access[0].admin.settings.setting_menu || {}
          ).map(([setting]) => (
            <div
              key={setting}
              className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
            >
              <span className="text-sm text-gray-700 capitalize">
                {setting.replace(/[_-]/g, " ")}
              </span>
              <div className="flex items-center space-x-3">
                <ToggleSwitch
                  checked={
                    data.nav_access[0].admin.settings.setting_menu[setting]
                  }
                  onChange={(checked) =>
                    onToggle(
                      "admin",
                      ["settings", "setting_menu", setting],
                      checked
                    )
                  }
                />
                <ToggleSwitch
                  checked={
                    data.nav_access[0].agent.settings.setting_menu[setting]
                  }
                  onChange={(checked) =>
                    onToggle(
                      "agent",
                      ["settings", "setting_menu", setting],
                      checked
                    )
                  }
                />
                <Popconfirm
                  title="Are you sure to delete this item?"
                  onConfirm={() =>
                    handleRemoveItem(["settings", "setting_menu"], setting)
                  }
                  okText="Yes"
                  cancelText="No"
                >
                  <button
                    className="text-red-500 hover:text-red-700"
                    title="Delete"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                </Popconfirm>
              </div>
            </div>
          ))}
          <div className="col-span-full">
            {editingSections["settings-setting_menu"] ? (
              <div className="flex items-center gap-2 p-2">
                <input
                  type="text"
                  value={newItems["settings-setting_menu"] || ""}
                  onChange={(e) =>
                    handleNewItemChange("settings-setting_menu", e.target.value)
                  }
                  className="flex-1 px-2 py-1 text-sm border border-gray-300 rounded"
                  placeholder="Enter new setting name"
                />
                <button
                  onClick={() =>
                    handleSaveNewItem(["settings", "setting_menu"])
                  }
                  className="px-2 py-1 bg-blue-500 text-white text-sm rounded hover:bg-blue-600"
                >
                  Add
                </button>
                <button
                  onClick={() =>
                    setEditingSections((prev) => ({
                      ...prev,
                      ["settings-setting_menu"]: false,
                    }))
                  }
                  className="px-2 py-1 bg-gray-300 text-gray-700 text-sm rounded hover:bg-gray-400"
                >
                  Cancel
                </button>
              </div>
            ) : (
              <button
                onClick={() => handleAddClick("settings-setting_menu")}
                className="w-full flex items-center justify-center gap-1 p-2 text-sm text-blue-500 hover:text-blue-700 hover:bg-blue-50 rounded"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 4v16m8-8H4"
                  />
                </svg>
                Add Setting
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

interface ToggleSwitchProps {
  checked: boolean;
  onChange: (checked: boolean) => void;
}

const ToggleSwitch: React.FC<ToggleSwitchProps> = ({ checked, onChange }) => (
  <label className="relative inline-flex items-center cursor-pointer">
    <input
      type="checkbox"
      checked={checked}
      onChange={(e) => onChange(e.target.checked)}
      className="sr-only peer"
    />
    <div
      className={`w-9 h-5 rounded-full peer ${
        checked ? "bg-gray-800" : "bg-gray-300"
      }`}
    >
      <div
        className={`absolute top-[2px] left-[2px] bg-white border border-gray-300 rounded-full h-4 w-4 transition-all ${
          checked ? "transform translate-x-4" : ""
        }`}
      ></div>
    </div>
  </label>
);

export default NavAccess;
