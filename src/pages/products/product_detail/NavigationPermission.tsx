import React, { useState } from "react";
import { AccessData } from "./AccessManagementPage";
import Popconfirm from "antd/es/popconfirm";

interface NavPermissionProps {
  data: AccessData;
  onToggle: (role: "admin" | "agent", path: string[], value: boolean) => void;
  onAddItem: (path: string[], newItem: string) => void;
  onRemoveItem: (path: string[], item: string) => void;
  onPermissionToggle?: (
    role: "admin" | "agent",
    permission: string,
    value: boolean
  ) => void;
  onSave?: () => Promise<void>; // Add onSave prop for auto-save
}

const NavPermission: React.FC<NavPermissionProps> = ({
  data,
  onPermissionToggle,
  onAddItem,
  onRemoveItem,
  onSave,
}) => {
  const [newPermission, setNewPermission] = useState("");
  const [isAdding, setIsAdding] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  const handleAddPermission = async () => {
    if (newPermission.trim()) {
      onAddItem(["nav_permission"], newPermission.trim());
      setNewPermission("");
      setIsAdding(false);

      // Auto-save AFTER React finishes updating state
      if (onSave) {
        setIsSaving(true);
        setTimeout(async () => {
          try {
            await onSave(); // Will now send updated data
          } catch (error) {
            console.error("Auto-save failed:", error);
          } finally {
            setIsSaving(false);
          }
        }, 0); // <- Ensures state is updated first
      }
    }
  };

  const handleRemovePermission = async (permission: string) => {
    onRemoveItem(["nav_permission"], permission);

    // Auto-save after removing
    if (onSave) {
      setIsSaving(true);
      try {
        await onSave();
      } catch (error) {
        console.error("Auto-save failed:", error);
      } finally {
        setIsSaving(false);
      }
    }
  };

  const PlusIcon = () => (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      className="h-4 w-4"
      fill="none"
      viewBox="0 0 24 24"
      stroke="currentColor"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M12 4v16m8-8H4"
      />
    </svg>
  );

  const TrashIcon = ({ className = "h-4 w-4" }: { className?: string }) => (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      fill="none"
      viewBox="0 0 24 24"
      stroke="currentColor"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
      />
    </svg>
  );

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleAddPermission();
    }
  };

  return (
    <div className="bg-white p-6 rounded-xl border border-gray-200 shadow-xs">
      <div className="flex justify-between items-center mr-5">
        <h3 className="font-medium text-gray-800 mb-4 text-lg">
          Navigation Permissions
          {isSaving && (
            <span className="ml-2 text-sm text-blue-500">Saving...</span>
          )}
        </h3>
        <div>
          <kbd>admin |</kbd>
          <kbd> agent</kbd>
        </div>
      </div>
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
        {Object.entries(data.nav_permission[0].admin).map(([permission]) => (
          <div
            key={permission}
            className="group flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
          >
            <span className="text-sm text-gray-700">
              {permission.replace(/_/g, " ")}
            </span>
            <div className="flex items-center space-x-3">
              <div className="flex space-x-3">
                <ToggleSwitch
                  checked={data.nav_permission[0].admin[permission]}
                  onChange={(checked) =>
                    onPermissionToggle?.("admin", permission, checked)
                  }
                />
                <ToggleSwitch
                  checked={data.nav_permission[0].agent[permission]}
                  onChange={(checked) =>
                    onPermissionToggle?.("agent", permission, checked)
                  }
                />
              </div>
              <Popconfirm
                title="Are you sure to delete this permission?"
                onConfirm={() => handleRemovePermission(permission)}
                okText="Yes"
                cancelText="No"
              >
                <button
                  className="text-red-500 hover:text-red-700"
                  title="Remove permission"
                  disabled={isSaving}
                >
                  <TrashIcon />
                </button>
              </Popconfirm>
            </div>
          </div>
        ))}
      </div>

      {/* Add new permission section */}
      {isAdding ? (
        <div className="flex items-center gap-2 mt-4 p-3 bg-gray-50 rounded-lg">
          <input
            type="text"
            value={newPermission}
            onChange={(e) => setNewPermission(e.target.value)}
            onKeyPress={handleKeyPress}
            className="flex-1 px-3 py-2 text-sm border border-gray-300 rounded"
            placeholder="Enter new permission name"
            autoFocus
          />
          <button
            onClick={handleAddPermission}
            disabled={!newPermission.trim() || isSaving}
            className="px-3 py-2 bg-blue-500 text-white text-sm rounded hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSaving ? "Adding..." : "Add"}
          </button>
          <button
            onClick={() => {
              setIsAdding(false);
              setNewPermission("");
            }}
            disabled={isSaving}
            className="px-3 py-2 bg-gray-300 text-gray-700 text-sm rounded hover:bg-gray-400 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Cancel
          </button>
        </div>
      ) : (
        <button
          onClick={() => setIsAdding(true)}
          disabled={isSaving}
          className="mt-4 w-full flex items-center justify-center gap-1 p-2 text-sm text-blue-500 hover:text-blue-700 hover:bg-blue-50 rounded disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <PlusIcon />
          Add New Permission
        </button>
      )}
    </div>
  );
};

interface ToggleSwitchProps {
  checked: boolean;
  onChange: (checked: boolean) => void;
}

const ToggleSwitch: React.FC<ToggleSwitchProps> = ({ checked, onChange }) => (
  <label className="relative inline-flex items-center cursor-pointer">
    <input
      type="checkbox"
      checked={checked}
      onChange={(e) => {
        e.stopPropagation();
        onChange(e.target.checked);
      }}
      className="sr-only peer"
    />
    <div
      className={`w-9 h-5 rounded-full peer ${
        checked ? "bg-gray-800" : "bg-gray-300"
      }`}
    >
      <div
        className={`absolute top-[2px] left-[2px] bg-white border border-gray-300 rounded-full h-4 w-4 transition-all ${
          checked ? "transform translate-x-4" : ""
        }`}
      ></div>
    </div>
  </label>
);

export default NavPermission;
