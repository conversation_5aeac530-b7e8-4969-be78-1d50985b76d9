import { Icon } from "@iconify/react";

interface ProfileInfoProps {
  profile: {
    name?: string;
    org_name?: string;
    org_email?: string;
    org_contact?: string;
    agent_goal?: string;
    additional_agent_goal?: string;
    agent_goal_type?: string;
    preferred_prompt?: string;
    org_type?: string;
    agent_name?: string;
    language?: string[];
    used_tools?: string[];
    ticket_required_info?: string[];
    set_up_complete?: boolean;
  };
}

const ProfileInfo = ({ profile }: ProfileInfoProps) => {
  console.log("Profile in component:", profile);

  if (!profile) {
    return (
      <div className="bg-white rounded-2xl p-6 border border-gray-200 shadow-sm">
        <div className="text-center text-gray-500 py-8">
          <Icon
            icon="solar:info-circle-bold"
            className="text-3xl mx-auto mb-3 text-gray-400"
          />
          <p>No profile data available</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-2xl p-6 border border-gray-200 shadow-sm">
      <h3 className="text-lg font-semibold text-gray-900 mb-6 flex items-center gap-2">
        <Icon icon="solar:user-id-bold" className="text-gray-700" />
        Tenant Profile Information
      </h3>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {/* Basic Information */}
        <div className="flex items-center gap-3 p-4 bg-gray-50 rounded-xl">
          <div className="p-2 bg-gray-200 rounded-lg">
            <Icon icon="solar:home-2-bold" className="text-gray-700" />
          </div>
          <div>
            <div className="text-sm text-gray-600">Organization Name</div>
            <div className="font-medium text-gray-900">
              {profile.org_name || "-"}
            </div>
          </div>
        </div>

        <div className="flex items-center gap-3 p-4 bg-gray-50 rounded-xl">
          <div className="p-2 bg-gray-200 rounded-lg">
            <Icon icon="solar:letter-bold" className="text-gray-700" />
          </div>
          <div>
            <div className="text-sm text-gray-600">Organization Email</div>
            <div className="font-medium text-gray-900">
              {profile.org_email || "-"}
            </div>
          </div>
        </div>

        <div className="flex items-center gap-3 p-4 bg-gray-50 rounded-xl">
          <div className="p-2 bg-gray-200 rounded-lg">
            <Icon icon="solar:phone-bold" className="text-gray-700" />
          </div>
          <div>
            <div className="text-sm text-gray-600">Contact Number</div>
            <div className="font-medium text-gray-900">
              {profile.org_contact || "-"}
            </div>
          </div>
        </div>

        {/* Agent Configuration */}
        <div className="flex items-center gap-3 p-4 bg-gray-50 rounded-xl">
          <div className="p-2 bg-gray-200 rounded-lg">
            <Icon icon="solar:user-bold" className="text-gray-700" />
          </div>
          <div>
            <div className="text-sm text-gray-600">Agent Name</div>
            <div className="font-medium text-gray-900">
              {profile.agent_name || "-"}
            </div>
          </div>
        </div>

        <div className="flex items-center gap-3 p-4 bg-gray-50 rounded-xl">
          <div className="p-2 bg-gray-200 rounded-lg">
            <Icon icon="solar:settings-bold" className="text-gray-700" />
          </div>
          <div>
            <div className="text-sm text-gray-600">Agent Goal Type</div>
            <div className="font-medium text-gray-900">
              {profile.agent_goal_type || "-"}
            </div>
          </div>
        </div>

        <div className="flex items-center gap-3 p-4 bg-gray-50 rounded-xl">
          <div className="p-2 bg-gray-200 rounded-lg">
            <Icon icon="solar:glasses-bold" className="text-gray-700" />
          </div>
          <div>
            <div className="text-sm text-gray-600">Preferred Prompt</div>
            <div className="font-medium text-gray-900">
              {profile.preferred_prompt || "-"}
            </div>
          </div>
        </div>

        {/* Additional Information */}
        <div className="flex items-center gap-3 p-4 bg-gray-50 rounded-xl">
          <div className="p-2 bg-gray-200 rounded-lg">
            <Icon icon="solar:documents-bold" className="text-gray-700" />
          </div>
          <div>
            <div className="text-sm text-gray-600">Used Tools</div>
            <div className="flex flex-wrap gap-2 font-medium text-gray-900">
              {profile.used_tools && profile.used_tools.length > 0 ? (
                profile.used_tools.map((tool) => (
                  <span
                    key={tool}
                    className="bg-gray-200 text-gray-800 text-xs font-semibold px-2.5 py-0.5 rounded"
                  >
                    {tool.charAt(0).toUpperCase() + tool.slice(1)}
                  </span>
                ))
              ) : (
                <span className="text-gray-400">-</span>
              )}
            </div>
          </div>
        </div>

        <div className="flex items-center gap-3 p-4 bg-gray-50 rounded-xl">
          <div className="p-2 bg-gray-200 rounded-lg">
            <Icon icon="solar:clipboard-text-bold" className="text-gray-700" />
          </div>
          <div>
            <div className="text-sm text-gray-600">Ticket Required Info</div>
            <div className="font-medium text-gray-900">
              {profile.ticket_required_info &&
              profile.ticket_required_info.length > 0 ? (
                profile.ticket_required_info.map((tool) => (
                  <span
                    key={tool}
                    className="bg-gray-200 text-gray-800 text-xs font-semibold px-2.5 py-0.5 rounded"
                  >
                    {tool.charAt(0).toUpperCase() + tool.slice(1)}
                  </span>
                ))
              ) : (
                <span className="text-gray-400">-</span>
              )}
            </div>
          </div>
        </div>

        <div className="flex items-center gap-3 p-4 bg-gray-50 rounded-xl">
          <div className="p-2 bg-gray-200 rounded-lg">
            <Icon icon="solar:shield-check-bold" className="text-gray-700" />
          </div>
          <div>
            <div className="text-sm text-gray-600">Setup Status</div>
            <div className="font-medium text-gray-900">
              {profile.set_up_complete ? (
                <span className="text-gray-700">Complete</span>
              ) : (
                <span className="text-gray-500">Incomplete</span>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfileInfo;
