import React, { useEffect, useState } from "react";
import baseHttp from "../../../services/http/base.http";
import PromptEditorListComponent from "@/components/PromptEditorListComponent";

interface Prompt {
  _id: string;
  name: string;
  text: string;
  model: string;
  description?: string;
  label?: string;
}

const TenantPromptPage: React.FC<{ tenant_db_name: string }> = ({
  tenant_db_name,
}) => {
  const [prompts, setPrompts] = useState<Prompt[]>([]);
  const [selectedPromptId, setSelectedPromptId] = useState<string>("");
  const [formState, setFormState] = useState<Prompt | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isCreating, setIsCreating] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showPromptList, setShowPromptList] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");

  // Fetch all prompts
  const fetchPrompts = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await baseHttp.get("/product-info/tenant-prompt", {
        params: { tenant_db_name },
      });
      const fetchedPrompts = response.data.prompts || [];
      setPrompts(fetchedPrompts);
      if (fetchedPrompts.length > 0) {
        setSelectedPromptId(fetchedPrompts[0]._id);
        setFormState(fetchedPrompts[0]);
      }
    } catch (err: any) {
      setError(err.message || "Failed to fetch prompts");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPrompts();
  }, [tenant_db_name]);

  const handleChange = (field: keyof Prompt, value: string) => {
    if (!formState) return;
    setFormState({ ...formState, [field]: value });
  };

  const handleSelectPrompt = async (promptId: string) => {
    try {
      const response = await baseHttp.get(
        `/product-info/tenant-prompt/${promptId}`,
        {
          params: { tenant_db_name },
        }
      );
      setFormState(response.data);
      setSelectedPromptId(promptId);
      setIsCreating(false);
    } catch (err: any) {
      setError(err.message || "Failed to fetch prompt");
    }
  };

  const handleCreateNew = () => {
    setFormState({
      _id: "",
      name: "",
      text: "",
      model: "",
      description: "",
      label: "",
    });
    setIsCreating(true);
  };

 const handleSave = async () => {
  if (!formState) return;
  setLoading(true);
  setError(null);

  try {
    // Prepare the data to send (exclude _id when creating)
    const payload = { ...formState };
    if (isCreating) {
      delete payload._id;
    }

    if (isCreating) {
      // Create new prompt
      const response = await baseHttp.post(
        "/product-info/tenant-prompt",
        payload,
        { params: { tenant_db_name } }
      );
      const createdPrompt = response.data;
      setPrompts(prev => [...prev, createdPrompt]);
      setFormState(createdPrompt);
      setSelectedPromptId(createdPrompt._id);
    } else {
      // Update existing prompt
      const response = await baseHttp.put(
        `/product-info/tenant-prompt/${formState._id}`,
        payload,
        { params: { tenant_db_name } }
      );
      const updatedPrompt = response.data;
      setPrompts(prev =>
        prev.map(p => (p._id === updatedPrompt._id ? updatedPrompt : p))
      );
      setFormState(updatedPrompt);
    }
    setIsCreating(false);
  } catch (err: any) {
    setError(err.response?.data?.detail || err.message || "Failed to save prompt");
  } finally {
    setLoading(false);
  }
};

  const handleDelete = async () => {
    if (!formState || !formState._id) return;
    setLoading(true);
    setError(null);

    try {
      await baseHttp.delete(`/product-info/tenant-prompt/${formState._id}`, {
        params: { tenant_db_name },
      });
      const updated = prompts.filter((p) => p._id !== formState._id);
      setPrompts(updated);
      setFormState(updated[0] || null);
      setSelectedPromptId(updated[0]?._id || "");
      setShowDeleteModal(false);
    } catch (err: any) {
      setError(err.message || "Failed to delete prompt");
    } finally {
      setLoading(false);
    }
  };

  const filteredPrompts = prompts.filter(
    (prompt) =>
      prompt.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      prompt.text.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <PromptEditorListComponent
      prompts={filteredPrompts}
      selectedPrompt={formState}
      isCreating={isCreating}
      loading={loading}
      error={error}
      showDeleteModal={showDeleteModal}
      showPromptList={showPromptList}
      searchTerm={searchTerm}
      onChangeField={handleChange}
      onSave={handleSave}
      onDelete={handleDelete}
      onCreateNew={handleCreateNew}
      onSelectPrompt={(prompt) => handleSelectPrompt(prompt._id)}
      setSearchTerm={setSearchTerm}
      setShowPromptList={setShowPromptList}
      setShowDeleteModal={setShowDeleteModal}
    />
  );
};

export default TenantPromptPage;
