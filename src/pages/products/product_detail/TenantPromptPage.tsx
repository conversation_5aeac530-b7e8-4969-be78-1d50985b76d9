import React, { useEffect, useState } from "react";
import baseHttp from "../../../services/http/base.http";

interface Prompt {
  _id: string;
  name: string;
  text: string;
  model: string;
  description?: string;
  label?: string;
}

const TenantPromptPage: React.FC<{ tenant_db_name: string }> = ({
  tenant_db_name,
}) => {
  const [prompts, setPrompts] = useState<Prompt[]>([]);
  const [selectedPromptId, setSelectedPromptId] = useState<string>("");
  const [formState, setFormState] = useState<Prompt | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPrompts = async () => {
      setLoading(true);
      setError(null);
      try {
        const response = await baseHttp.get("/product-info/tenant-prompt", {
          params: { tenant_db_name },
        });
        const fetchedPrompts = response.data.prompts || [];
        setPrompts(fetchedPrompts);
        if (fetchedPrompts.length > 0) {
          setSelectedPromptId(fetchedPrompts[0]._id);
        }
      } catch (err: any) {
        setError(err.message || "Failed to fetch prompts");
      } finally {
        setLoading(false);
      }
    };

    fetchPrompts();
  }, [tenant_db_name]);

  useEffect(() => {
    const selected = prompts.find((p) => p._id === selectedPromptId);
    if (selected) {
      setFormState({ ...selected });
    }
  }, [selectedPromptId, prompts]);

  const handleChange = (field: keyof Prompt, value: string) => {
    if (!formState) return;
    setFormState({ ...formState, [field]: value });
  };

  const handleSave = () => {
    if (!formState) return;

    setPrompts((prev) =>
      prev.map((p) => (p._id === formState._id ? formState : p))
    );

    // Optionally send a PUT request here to update backend
  };

  if (loading)
    return <p className="p-8 text-center text-gray-700">Loading prompts...</p>;
  if (error)
    return (
      <div className="p-8 text-center text-gray-700">
        <p>{error}</p>
      </div>
    );

  return (
    <div className="max-w-6xl mx-auto p-6 bg-white border border-gray-200 rounded-xl shadow-sm">
      <div className="flex gap-6 mb-6">
        {/* Select Prompt */}
        <div className="w-1/2">
          <label className="block text-sm font-medium mb-1 text-gray-800">
            Select Prompt:
          </label>
          <select
            value={selectedPromptId}
            onChange={(e) => setSelectedPromptId(e.target.value)}
            className="w-full p-2 border border-gray-300 rounded-lg focus:ring-gray-500 focus:border-gray-500"
          >
            {prompts.map((prompt) => (
              <option key={prompt._id} value={prompt._id}>
                {prompt.label || prompt.name}
              </option>
            ))}
          </select>
        </div>

        {/* Model Field */}
        <div className="w-1/2">
          <label className="block text-sm font-medium mb-1 text-gray-800">
            Model:
          </label>
          <input
            type="text"
            value={formState?.model || ""}
            onChange={(e) => handleChange("model", e.target.value)}
            className="w-full p-2 border border-gray-300 rounded-lg focus:ring-gray-500 focus:border-gray-500"
          />
        </div>
      </div>

      {formState && (
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-1 text-gray-800">
              Prompt Text:
            </label>
            <textarea
              rows={13}
              value={formState.text}
              onChange={(e) => handleChange("text", e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-lg font-mono resize-y overflow-y-auto focus:ring-gray-500 focus:border-gray-500"
            />
          </div>

          <button
            onClick={handleSave}
            className="px-4 py-2 bg-gray-800 text-white rounded-lg hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
          >
            Save Changes
          </button>
        </div>
      )}
    </div>
  );
};

export default TenantPromptPage;
