import React, { useEffect, useState } from "react";
import baseHttp from "../../../services/http/base.http";

interface ToolInfo {
  enabled: boolean;
  priority: number;
  description: string;
}

interface ToolsAccess {
  enabled: boolean;
  available_tools: Record<string, ToolInfo>;
  tool_permissions: {
    admin: string[];
    agent: string[];
    supervisor: string[];
  };
}

const TenantToolsPage: React.FC<{ tenant_db_name: string }> = ({
  tenant_db_name,
}) => {
  const [toolsAccess, setToolsAccess] = useState<ToolsAccess | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [editError, setEditError] = useState<string | null>(null);
  const [editSuccess, setEditSuccess] = useState<boolean>(false);

  useEffect(() => {
    const fetchTools = async () => {
      setLoading(true);
      setError(null);
      try {
        const res = await baseHttp.get("/product-info/tenant-tools", {
          params: { tenant_db_name },
        });
        setToolsAccess(res.data.tools_access);
      } catch (err) {
        setError("Failed to load tenant tools");
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    fetchTools();
  }, [tenant_db_name]);

  const handleToggleEnable = async () => {
    if (!toolsAccess) return;

    try {
      setEditError(null);
      const newStatus = !toolsAccess.enabled;
      await baseHttp.put("/product-info/tenant-tools", {
        tenant_db_name,
        enabled: newStatus,
      });
      setToolsAccess({ ...toolsAccess, enabled: newStatus });
      setEditSuccess(true);
      setTimeout(() => setEditSuccess(false), 3000);
    } catch (err) {
      setEditError("Failed to update tool status");
      console.error(err);
    }
  };

  const handleToolToggle = async (toolName: string, currentStatus: boolean) => {
    if (!toolsAccess) return;

    try {
      setEditError(null);
      const newStatus = !currentStatus;
      await baseHttp.put("/product-info/tenant-tools", {
        tenant_db_name,
        tool_name: toolName,
        enabled: newStatus,
      });

      setToolsAccess({
        ...toolsAccess,
        available_tools: {
          ...toolsAccess.available_tools,
          [toolName]: {
            ...toolsAccess.available_tools[toolName],
            enabled: newStatus,
          },
        },
      });
      setEditSuccess(true);
      setTimeout(() => setEditSuccess(false), 3000);
    } catch (err) {
      setEditError(`Failed to update ${toolName} status`);
      console.error(err);
    }
  };

  const handlePermissionChange = async (
    role: string,
    toolName: string,
    isAdding: boolean
  ) => {
    if (!toolsAccess) return;

    try {
      setEditError(null);
      await baseHttp.put("/product-info/tenant-tools", {
        tenant_db_name,
        role,
        tool_name: toolName,
        action: isAdding ? "add" : "remove",
      });

      const updatedPermissions = { ...toolsAccess.tool_permissions };
      if (isAdding) {
        updatedPermissions[role as keyof typeof updatedPermissions] = [
          ...(updatedPermissions[role as keyof typeof updatedPermissions] ||
            []),
          toolName,
        ];
      } else {
        updatedPermissions[role as keyof typeof updatedPermissions] =
          updatedPermissions[role as keyof typeof updatedPermissions].filter(
            (tool) => tool !== toolName
          );
      }

      setToolsAccess({
        ...toolsAccess,
        tool_permissions: updatedPermissions,
      });
      setEditSuccess(true);
      setTimeout(() => setEditSuccess(false), 3000);
    } catch (err) {
      setEditError("Failed to update permissions");
      console.error(err);
    }
  };

  if (loading)
    return (
      <div className="p-4 text-center text-gray-700">Loading tools...</div>
    );

  if (error) return <div className="p-4 text-center text-red-600">{error}</div>;

  if (!toolsAccess)
    return (
      <div className="p-4 text-center text-gray-700">
        No tools data available
      </div>
    );

  return (
    <div className="max-w-7xl mx-auto p-6 bg-white rounded-xl border border-gray-200 shadow-sm">
      {editError && (
        <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-md">
          {editError}
        </div>
      )}
      {editSuccess && (
        <div className="mb-4 p-3 bg-green-100 text-green-700 rounded-md">
          Changes saved successfully!
        </div>
      )}

      <div className="flex justify-between items-center mr-5">
        <div>
          <h2 className="text-xl font-semibold text-gray-800 mb-6">
            Tenant Tools Access
          </h2>
        </div>
        <div className="flex items-center gap-4">
          <button
            onClick={() => setIsEditing(!isEditing)}
            className={`px-4 py-2 rounded-md ${
              isEditing ? "bg-gray-200 text-gray-800" : "bg-blue-600 text-white"
            }`}
          >
            {isEditing ? "Close Edit Mode" : "Edit"}
          </button>
          <div
            onClick={isEditing ? handleToggleEnable : undefined}
            className={`inline-block px-3 py-1 text-sm rounded-full cursor-pointer ${
              toolsAccess.enabled
                ? "bg-black/20 text-black"
                : "bg-red-100 text-red-800"
            } ${isEditing ? "hover:opacity-80" : ""}`}
          >
            Overall Tool status: {toolsAccess.enabled ? "Enabled" : "Disabled"}
            {isEditing && (
              <span className="ml-2 text-xs">
                (Click to {toolsAccess.enabled ? "disable" : "enable"})
              </span>
            )}
          </div>
        </div>
      </div>

      <div className="mb-6"></div>

      <div className="mb-8">
        <h3 className="text-lg font-medium text-gray-800 mb-3">
          Available Tools
        </h3>
        <div className="space-y-4">
          {Object.entries(toolsAccess.available_tools)
            .sort(([, a], [, b]) => a.priority - b.priority)
            .map(([toolName, tool]) => (
              <div
                key={toolName}
                className="border border-gray-300 rounded-lg p-4 hover:shadow-md transition-shadow"
              >
                <div className="flex justify-between items-center mb-1">
                  <div>
                    <h4 className="text-md font-semibold capitalize text-gray-900">
                      {toolName.replace(/_/g, " ")}
                    </h4>
                    <span>Priority {tool.priority}</span>
                  </div>
                  {isEditing ? (
                    <div className="flex flex-col items-start space-y-1">
                      <p className="text-sm font-medium">Current status:</p>
                      <button
                        onClick={() => handleToolToggle(toolName, tool.enabled)}
                        className={`text-sm font-medium px-3 py-1 rounded-full ${
                          tool.enabled
                            ? "bg-black/20 text-black hover:bg-black/30"
                            : "bg-red-100 text-red-800 hover:bg-red-200"
                        }`}
                      >
                        {tool.enabled ? "Disable" : "Enable"}
                      </button>
                    </div>
                  ) : (
                    <div className="flex flex-col items-start space-y-1">
                      <p className="text-sm font-medium">Current status</p>
                      <span
                        className={`text-sm font-medium px-2 py-1 rounded-full ${
                          tool.enabled
                            ? "bg-black/20 text-black"
                            : "bg-red-100 text-red-800"
                        }`}
                      >
                        {tool.enabled ? "Enabled" : "Disabled"}
                      </span>
                    </div>
                  )}
                </div>
                <p className="text-gray-600 text-sm">{tool.description}</p>
              </div>
            ))}
        </div>
      </div>

      <div>
        <h3 className="text-lg font-medium text-gray-800 mb-3">
          Tool Permissions
        </h3>
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-6">
          {["admin", "agent", "supervisor"].map((role) => (
            <div
              key={role}
              className="bg-gray-50 p-4 rounded-lg border border-gray-200"
            >
              <h4 className="text-md font-semibold text-gray-900 capitalize mb-2">
                {role}
              </h4>
              {toolsAccess.tool_permissions[
                role as keyof typeof toolsAccess.tool_permissions
              ]?.length ? (
                <ul className="list-disc list-inside text-gray-700 text-sm space-y-1">
                  {toolsAccess.tool_permissions[
                    role as keyof typeof toolsAccess.tool_permissions
                  ].map((tool) => (
                    <li
                      key={tool}
                      className="flex justify-between items-center"
                    >
                      <span>{tool.replace(/_/g, " ")}</span>
                      {isEditing && (
                        <button
                          onClick={() =>
                            handlePermissionChange(role, tool, false)
                          }
                          className="text-red-500 hover:text-red-700 text-xs"
                        >
                          Remove
                        </button>
                      )}
                    </li>
                  ))}
                </ul>
              ) : (
                <p className="text-gray-500 text-sm italic">
                  No permissions assigned
                </p>
              )}

              {isEditing && (
                <div className="mt-4">
                  <h5 className="text-sm font-medium text-gray-700 mb-2">
                    Add Tool Permission:
                  </h5>
                  <select
                    onChange={(e) => {
                      if (e.target.value) {
                        handlePermissionChange(role, e.target.value, true);
                        e.target.value = ""; // Reset selection
                      }
                    }}
                    className="w-full p-2 border border-gray-300 rounded-md text-sm"
                  >
                    <option value="">Select a tool...</option>
                    {Object.keys(toolsAccess.available_tools)
                      .filter(
                        (tool) =>
                          !toolsAccess.tool_permissions[
                            role as keyof typeof toolsAccess.tool_permissions
                          ]?.includes(tool)
                      )
                      .map((tool) => (
                        <option key={tool} value={tool}>
                          {tool.replace(/_/g, " ")}
                        </option>
                      ))}
                  </select>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default TenantToolsPage;
