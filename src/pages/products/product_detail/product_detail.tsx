import React, { useEffect, useState } from "react";
import baseHttp from "../../../services/http/base.http";
import { Icon } from "@iconify/react";
import { useLocation, useNavigate } from "react-router-dom";
import TenantPromptPage from "./TenantPromptPage";
import ProfileInfo from "./ProfileInfo";
import CreditSummary from "./CreditSummary";
import AccessManagementPage from "./AccessManagementPage";
import TenantToolsPage from "./ToolManagementPage";
import ExtraEko from "../eko_admin/ExtraEko";

const tabs = [
  { id: "summary", label: "Overview", icon: "solar:chart-2-bold" },
  { id: "prompt", label: "Prompts", icon: "solar:code-bold" },
  { id: "tools", label: "Tools", icon: "solar:widget-bold" },
  { id: "access", label: "Access", icon: "solar:key-bold" },
  { id: "more", label: "More", icon: "tabler:dots"}

];

const ProductDetail: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const tenant_db_name = location.state?.tenant_db_name ?? null;

  const [activeTab, setActiveTab] = useState("summary");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [data, setData] = useState<any>(null);
  const [profile, setProfile] = useState<any>(null);

  useEffect(() => {
    if (!tenant_db_name) {
      setError("Tenant database name not provided.");
      setLoading(false);
      return;
    }

    const fetchData = async () => {
      setLoading(true);
      setError(null);
      try {
        const response = await baseHttp.get("/product-info/tenant-info", {
          params: { tenant_db_name },
        });
        const response2 = await baseHttp.get("/product-info/tenant-profile", {
          params: { tenant_db_name },
        });
        setProfile(response2.data);
        setData(response.data);
      } catch (err: any) {
        setError(err.message || "Failed to fetch product info");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [tenant_db_name]);

  const renderSummary = () => {
    if (!data || !profile) return null;

    return (
      <div className="space-y-6">
        <ProfileInfo profile={profile} />
        <CreditSummary
          credit_info={data.credit_info}
          ai_status={data.ai_status}
        />
        <div className="bg-white rounded-2xl p-6 border border-gray-200 shadow-sm">
          <h3 className="text-lg font-semibold text-gray-900 mb-6 flex items-center gap-2">
            <Icon icon="solar:server-bold" className="text-black-500" />
            Qdrant Configuration Info
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center gap-3 p-4 bg-gray-50 rounded-xl">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Icon
                  icon="solar:document-text-bold"
                  className="text-blue-600"
                />
              </div>
              <div>
                <div className="text-sm text-gray-600">Sentence Collection</div>
                <div className="font-medium text-gray-900">
                  {data.env_config?.qdrant_config?.sentence_collection}
                </div>
              </div>
            </div>
            <div className="flex items-center gap-3 p-4 bg-gray-50 rounded-xl">
              <div className="p-2 bg-green-100 rounded-lg">
                <Icon icon="solar:document-bold" className="text-green-600" />
              </div>
              <div>
                <div className="text-sm text-gray-600">Page Collection</div>
                <div className="font-medium text-gray-900">
                  {data.env_config?.qdrant_config?.page_collection}
                </div>
              </div>
            </div>
            <div className="flex items-center gap-3 p-4 bg-gray-50 rounded-xl">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Icon icon="solar:layers-bold" className="text-purple-600" />
              </div>
              <div>
                <div className="text-sm text-gray-600">Split Collection</div>
                <div className="font-medium text-gray-900">
                  {data.env_config?.qdrant_config?.sentence_split_collection}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderPrompt = () => (
    <TenantPromptPage tenant_db_name={tenant_db_name} />
  );

  const renderTools = () => <TenantToolsPage tenant_db_name={tenant_db_name} />;

  const renderAccess = () => (
    <AccessManagementPage tenant_db_name={tenant_db_name} />
  );
    const renderMore = () => (
    <ExtraEko tenant_db_name={tenant_db_name} />
  );

  const renderContent = () => {
    switch (activeTab) {
      case "summary":
        return renderSummary();
      case "prompt":
        return renderPrompt();
      case "tools":
        return renderTools();
      case "access":
        return renderAccess();
      case "more":
        return renderMore();
      default:
        return null;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto "></div>
          <p className="text-gray-600 font-medium">
            Loading product details...
          </p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-8">
          <Icon
            icon="solar:danger-bold"
            className="text-red-500 text-6xl mx-auto mb-4"
          />
          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            Error Loading Data
          </h3>
          <p className="text-gray-600 mb-6">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="bg-red-600 text-white px-6 py-3 rounded-xl hover:bg-red-700 transition-colors font-medium"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Fixed Header + Tabs */}
      <div className="sticky top-0 z-20 bg-gray-50 w-full">
        <div className="max-w-7xl mx-auto px-4 py-1">
          <div className="flex flex-col mb-4">
            <div className="flex items-center gap-3 mb-1">
              <button
                onClick={() => navigate(-1)}
                className="p-2 text-gray-400 hover:text-gray-600 rounded-lg transition-colors"
                style={{ border: "none" }} // just in case any default border exists
              >
                <Icon icon="solar:arrow-left-bold" className="text-3xl" />
              </button>
              <h1 className="text-2xl font-bold text-gray-900">
                Product Dashboard
              </h1>
            </div>
            <p
              className="text-gray-600 text-sm self-left"
              style={{ textAlign: "justify", marginBottom: "0" }}
            >
              Manage your product configuration and monitor usage
            </p>
          </div>

          <div className="bg-white rounded-xl p-2 shadow-sm border border-gray-200">
            <div className="flex space-x-2">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center gap-2 px-6 py-3 rounded-xl font-medium transition-all duration-200 ${
                    activeTab === tab.id
                      ? "bg-black/70 text-white shadow-lg transform scale-105"
                      : "text-gray-600 hover:text-gray-900 hover:bg-gray-50"
                  }`}
                >
                  <Icon icon={tab.icon} className="text-lg" />
                  {tab.label}
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Scrollable Content */}
      <div className="max-w-7xl mx-auto px-4 py-8 animate-fadeIn">
        {renderContent()}
      </div>

      <style jsx>{`
        @keyframes fadeIn {
          from {
            opacity: 0;
            transform: translateY(20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        .animate-fadeIn {
          animation: fadeIn 0.5s ease-out;
        }
      `}</style>
    </div>
  );
};

export default ProductDetail;
