import React from "react";
import { Icon } from "@iconify/react";
import { useNavigate } from "react-router-dom";

interface ProductInfoProps {
  tenants: any[];
  total: number;
  skip: number;
  limit: number;
  loading: boolean;
  error: string | null;
  search: string;
  onSearchChange: (value: string) => void;
  onPageChange: (newSkip: number) => void;
}

const ProductInfo: React.FC<ProductInfoProps> = ({
  tenants,
  total,
  skip,
  limit,
  loading,
  error,
  search,
  onSearchChange,
  onPageChange,
}) => {
  const [expandedIndex, setExpandedIndex] = React.useState<number | null>(null);
  const navigate = useNavigate();

  const toggleExpand = (index: number) => {
    setExpandedIndex(expandedIndex === index ? null : index);
  };

  const totalPages = Math.ceil(total / limit);
  const currentPage = Math.floor(skip / limit) + 1;

  const handlePageClick = (page: number) => {
    const newSkip = (page - 1) * limit;
    onPageChange(newSkip);
  };
  const handleSettingsClick = (tenantName: string) => {
    navigate("/product-info", { state: { tenant_db_name: tenantName } });
  };

  const renderPageButtons = () => {
    const pages = [];
    const maxVisible = 5;
    const half = Math.floor(maxVisible / 2);
    let start = Math.max(1, currentPage - half);
    let end = Math.min(totalPages, start + maxVisible - 1);

    if (end - start + 1 < maxVisible) {
      start = Math.max(1, end - maxVisible + 1);
    }

    for (let i = start; i <= end; i++) {
      pages.push(
        <button
          key={i}
          onClick={() => handlePageClick(i)}
          className={`px-3 py-1 rounded-md border text-sm font-medium transition ${
            i === currentPage
              ? "bg-primary text-white border-primary"
              : "bg-muted text-foreground border-border hover:bg-accent"
          }`}
        >
          {i}
        </button>
      );
    }

    return pages;
  };

  const from = total === 0 ? 0 : skip + 1;
  const to = Math.min(skip + limit, total);

  return (
    <div
      className="bg-card rounded-2xl p-6 mt-5 border border-border animate-fade-in"
      style={{ animationDelay: "0.2s" }}
    >
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold text-foreground">Tenant List</h3>
        <input
          type="text"
          value={search}
          onChange={(e) => onSearchChange(e.target.value)}
          placeholder="Search tenants..."
          className="px-3 py-2 text-sm rounded-md border border-border bg-muted text-foreground focus:outline focus:ring-1 focus:ring-ring"
        />
      </div>

      {loading ? (
        <p className="text-muted-foreground">Loading tenants...</p>
      ) : error ? (
        <p className="text-red-500">Error: {error}</p>
      ) : tenants.length === 0 ? (
        <p className="text-muted-foreground">No tenants found.</p>
      ) : (
        <>
          <p className="text-sm text-muted-foreground mb-2">
            Showing {from}–{to} of {total} tenants
          </p>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            {tenants.map((tenant, index) => (
              <div
                key={index}
                className="relative cursor-pointer p-4 border border-border rounded-xl hover:bg-muted transition self-start h-full"
                onClick={() => toggleExpand(index)}
              >
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-foreground rounded-full"></div>
                  <div className="flex-1">
                    <p className="text-sm text-foreground font-medium">
                      {tenant.name}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      Click to {expandedIndex === index ? "hide" : "view"}{" "}
                      details
                    </p>
                  </div>
                  <button
                    className="text-muted-foreground hover:text-foreground"
                    onClick={(e) => {
                      e.stopPropagation();

                      // Search for key exactly "Database_name"
                      const dbNameEntry = Object.entries(tenant).find(
                        ([key]) => key === "database_name"
                      );

                      const dbName = dbNameEntry?.[1];

                      if (dbName) {
                        handleSettingsClick(dbName);
                      } else {
                        console.warn(
                          "Database name not found in tenant:",
                          tenant
                        );
                      }
                    }}
                  >
                    <Icon icon="mdi:cog" width="20" height="20" />
                  </button>
                </div>

                {expandedIndex === index && (
                  <div className="mt-3 ml-5 text-sm text-muted-foreground space-y-1">
                    {Object.entries(tenant).map(([key, value]) =>
                      key !== "name" ? (
                        <div key={key}>
                          <strong className="capitalize">{key}:</strong>{" "}
                          {String(value)}
                        </div>
                      ) : null
                    )}
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex justify-center gap-2 mt-6">
              {renderPageButtons()}
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default ProductInfo;
