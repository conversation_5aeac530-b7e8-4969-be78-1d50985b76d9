import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Building2, Users } from "lucide-react";
import { Button } from "@/components";
import ProductContainer from "./product_detail.container";

const ProductsDashboard: React.FC = () => {
  const [selectedTenant, setSelectedTenant] = useState<any>(null);
  const navigate = useNavigate();

  useEffect(() => {
    const tenant = localStorage.getItem("selected_tenant");
    if (tenant) {
      setSelectedTenant(JSON.parse(tenant));
    }
  }, []);

  const goToProducts = () => {
    navigate("/products");
  };

  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-foreground mb-2">
          {selectedTenant ? selectedTenant.name : "Dashboard"}
        </h1>
        <p className="text-muted-foreground">
          Welcome to your workspace overview
        </p>
      </div>

      {/* Workspace Info */}
      {selectedTenant && (
        <div className="bg-card rounded-2xl p-6 mb-8 border border-border animate-fade-in">
          <div className="flex items-center space-x-4 mb-4">
            <div className="bg-foreground p-3 rounded-xl">
              <Building2 className="h-6 w-6 text-background" />
            </div>
            <div>
              <h3 className="text-xl font-bold text-foreground">
                {selectedTenant.name}
              </h3>
              <p className="text-muted-foreground font-mono">
                /{selectedTenant.slug}
              </p>
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-background p-4 rounded-lg border border-border">
              <p className="text-sm text-muted-foreground">Database</p>
              <p className="font-mono text-foreground">
                {selectedTenant.database_name}
              </p>
            </div>
            <div className="bg-background p-4 rounded-lg border border-border">
              <p className="text-sm text-muted-foreground">Created</p>
              <p className="text-foreground">
                {new Date(selectedTenant.created_at).toLocaleDateString()}
              </p>
            </div>
            <div className="bg-background p-4 rounded-lg border border-border">
              <p className="text-sm text-muted-foreground">Status</p>
              <p className="text-foreground capitalize">
                {selectedTenant.status || "Active"}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {/* Quick Actions */}
        <div className="bg-card rounded-2xl p-6 border border-border animate-fade-in">
          <div className="flex items-center space-x-3 mb-4">
            <Users className="h-6 w-6 text-foreground" />
            <h3 className="text-lg font-semibold text-foreground">
              Quick Actions
            </h3>
          </div>
          <div className="space-y-3">
            <Button
              className="w-full bg-foreground hover:bg-foreground/90 text-background"
              onClick={() => {
                /* Add action */
              }}
            >
              Manage Users
            </Button>
            <Button
              variant="outline"
              className="w-full border-border text-foreground hover:bg-muted"
              onClick={() => {
                /* Add action */
              }}
            >
              View Reports
            </Button>
            <Button
              variant="outline"
              className="w-full border-border text-foreground hover:bg-muted"
              onClick={() => {
                navigate("/eko-admin");
              }}
            >
              Settings
            </Button>
          </div>
        </div>

        {/* Stats */}
        <div
          className="bg-card rounded-2xl p-6 border border-border animate-fade-in"
          style={{ animationDelay: "0.1s" }}
        >
          <h3 className="text-lg font-semibold text-foreground mb-4">
            Statistics
          </h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-muted-foreground">Total Users</span>
              <span className="font-bold text-foreground">
                {selectedTenant?.user_count || 0}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-muted-foreground">Active Sessions</span>
              <span className="font-bold text-foreground">1</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-muted-foreground">Storage Used</span>
              <span className="font-bold text-foreground">2.4 GB</span>
            </div>
          </div>
        </div>

        {/* Recent Activity */}
        <div
          className="bg-card rounded-2xl p-6 border border-border animate-fade-in"
          style={{ animationDelay: "0.2s" }}
        >
          <h3 className="text-lg font-semibold text-foreground mb-4">
            Recent Activity
          </h3>
          <div className="space-y-3">
            <div className="flex items-center space-x-3">
              <div className="w-2 h-2 bg-foreground rounded-full"></div>
              <div>
                <p className="text-sm text-foreground">Workspace created</p>
                <p className="text-xs text-muted-foreground">Just now</p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <div className="w-2 h-2 bg-muted-foreground rounded-full"></div>
              <div>
                <p className="text-sm text-foreground">User logged in</p>
                <p className="text-xs text-muted-foreground">2 minutes ago</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div>
        <ProductContainer />
      </div>

      {/* Welcome Message */}
      {!selectedTenant && (
        <div className="text-center py-16 animate-fade-in">
          <div className="mx-auto w-24 h-24 bg-muted rounded-full flex items-center justify-center mb-8">
            <Building2 className="h-12 w-12 text-muted-foreground" />
          </div>
          <h3 className="text-2xl font-bold text-foreground mb-4">
            Welcome to your Dashboard
          </h3>
          <p className="text-muted-foreground mb-8 text-lg">
            Select a workspace to get started
          </p>
          <Button
            onClick={goToProducts}
            className="bg-foreground hover:bg-foreground/90 text-background border-none shadow-xl hover-lift px-8 py-3 text-lg"
          >
            <Building2 className="w-5 h-5 mr-2" />
            View Products
          </Button>
        </div>
      )}
    </div>
  );
};

export default ProductsDashboard;
