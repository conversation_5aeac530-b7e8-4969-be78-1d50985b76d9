import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Building2, Plus, Calendar, Users } from 'lucide-react';
import { Button } from '@/components';
import { authService, Tenant } from '@/services';
import { toast } from '@/hooks/use-toast';

const ProductsList: React.FC = () => {
  const [tenants, setTenants] = useState<Tenant[]>([]);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  useEffect(() => {
    fetchTenants();
  }, []);

  const fetchTenants = async () => {
    try {
      const tenantList = await authService.getTenants();
      setTenants(tenantList);
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message,
      });
    } finally {
      setLoading(false);
    }
  };



  const selectTenant = (tenantId: string) => {
    const tenant = tenants.find(t => t.id === tenantId);
    if (tenant) {
      localStorage.setItem('selected_tenant', JSON.stringify(tenant));
      navigate(`/dashboard/${tenant.slug}`);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-foreground"></div>
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-foreground mb-2">Products Dashboard</h1>
        <p className="text-muted-foreground">Manage your business products and workspaces</p>
      </div>

      {/* Action Bar */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center space-x-4">
          <div className="bg-card p-4 rounded-xl border border-border animate-fade-in">
            <div className="flex items-center space-x-3">
              <Building2 className="h-6 w-6 text-foreground" />
              <div>
                <p className="text-sm text-muted-foreground">Total Products</p>
                <p className="text-2xl font-bold text-foreground">{tenants.length}</p>
              </div>
            </div>
          </div>
        </div>
        <div className="flex space-x-3 animate-slide-in-right">
          <Button
            onClick={() => navigate('/onboarding/create')}
            className="bg-foreground hover:bg-foreground/90 text-background border-none shadow-lg hover-lift flex items-center space-x-2"
          >
            <Plus size={20} />
            <span>Create Business</span>
          </Button>
        </div>
      </div>

      {/* Content */}
      {tenants.length === 0 ? (
        <div className="text-center py-16 animate-fade-in">
          <div className="mx-auto w-24 h-24 bg-muted rounded-full flex items-center justify-center mb-8">
            <Building2 className="h-12 w-12 text-muted-foreground" />
          </div>
          <h3 className="text-2xl font-bold text-foreground mb-4">No products found</h3>
          <p className="text-muted-foreground mb-8 text-lg">Get started by creating your first business</p>
          <Button
            onClick={() => navigate('/onboarding/create')}
            className="bg-foreground hover:bg-foreground/90 text-background border-none shadow-xl hover-lift px-8 py-3 text-lg"
          >
            <Plus className="w-5 h-5 mr-2" />
            Create Your First Business
          </Button>
        </div>
      ) : (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {tenants.map((tenant, index) => (
            <div
              key={tenant.id}
              className="glass-card rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer hover-lift animate-fade-in"
              style={{ animationDelay: `${index * 0.1}s` }}
              onClick={() => selectTenant(tenant.id)}
            >
                <div className="p-6">
                  <div className="flex items-start justify-between mb-6">
                    <div className="flex items-center space-x-4">
                      <div className="bg-foreground p-3 rounded-xl">
                        <Building2 className="h-6 w-6 text-background" />
                      </div>
                      <div>
                        <h3 className="text-xl font-bold text-foreground">{tenant.name}</h3>
                        <p className="text-muted-foreground font-mono text-sm">/{tenant.slug}</p>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <div className="flex items-center text-sm text-muted-foreground">
                      <Calendar className="h-4 w-4 mr-3" />
                      Created {new Date(tenant.created_at).toLocaleDateString()}
                    </div>
                    
                    {tenant.user_count !== undefined && (
                      <div className="flex items-center text-sm text-muted-foreground">
                        <Users className="h-4 w-4 mr-3" />
                        {tenant.user_count} users
                      </div>
                    )}

                    {tenant.status && (
                      <div className="flex items-center">
                        <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold ${
                          tenant.status === 'active' 
                            ? 'bg-green-100 text-green-800 border border-green-200' 
                            : 'bg-gray-100 text-gray-800 border border-gray-200'
                        }`}>
                          {tenant.status}
                        </span>
                      </div>
                    )}
                  </div>

                  <div className="mt-6 pt-4 border-t border-border">
                    <div className="flex items-center justify-between">
                      <p className="text-xs text-muted-foreground truncate font-mono">
                        DB: {tenant.database_name}
                      </p>
                      <Button
                        onClick={(e) => {
                          e.stopPropagation();
                          navigate(`/onboarding/create/${tenant.id}`);
                        }}
                        size="sm"
                        className="bg-foreground hover:bg-foreground/90 text-background border-none shadow-md hover-lift"
                      >
                        Create
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default ProductsList;
