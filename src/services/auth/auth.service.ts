import axios from "axios";
import baseHttp from "../http/base.http";

export interface LoginCredentials {
  username: string;
  password: string;
}

export interface LoginResponse {
  access_token: string;
  token_type: string;
  user: {
    id: string;
    username: string;
    role: string;
  };
}

export interface Tenant {
  id: string;
  name: string;
  slug: string;
  database_name: string;
  created_at: string;
  user_count?: number;
  status?: string;
}

export interface CreateTenantData {
  business_name: string;
  company_slug: string;
  product: string;
  username: string;
  org_type: string;
}

export interface OnboardingData {
  business_name: string;
  company_slug: string;
  user_role: string;
}

export interface CreateTenantResponse {
  success: boolean;
  message: string;
  tenant_id: string;
  tenant_slug: string;
  database_name: string;
  invitation_link: string;
}

export interface OrgType {
  name: string;
  label: string;
}

class AuthService {
  /**
   * Authenticate user and get access token
   */
  async login(credentials: LoginCredentials): Promise<LoginResponse> {
    try {
      // Prepare OAuth2 password flow data
      const formData = new URLSearchParams();
      formData.append("grant_type", "password");
      formData.append("username", credentials.username);
      formData.append("password", credentials.password);
      formData.append("scope", "");
      formData.append("client_id", "string");
      formData.append("client_secret", "********");

      const response = await baseHttp.post<LoginResponse>(
        "/auth/login",
        formData,
        {
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
            accept: "application/json",
          },
        }
      );

      // Store token and user data
      localStorage.setItem("access_token", response.data.access_token);
      localStorage.setItem("user", JSON.stringify(response.data.user));

      return response.data;
    } catch (error: any) {
      console.error("Login error:", error);
      throw new Error(error.response?.data?.detail || "Login failed");
    }
  }

  /**
   * Get list of all tenants (Admin only)
   */
  async getTenants(): Promise<Tenant[]> {
    try {
      const response = await baseHttp.get<{
        success: boolean;
        tenants: Tenant[];
      }>("/tenants/list", {
        headers: {
          accept: "application/json",
        },
      });
      return response.data.tenants;
    } catch (error: any) {
      console.error("Get tenants error:", error);
      throw new Error(
        error.response?.data?.detail || "Failed to fetch tenants"
      );
    }
  }

  /**
   * Get specific tenant details by ID
   */
  async getTenantDetails(tenantId: string): Promise<Tenant> {
    try {
      const response = await baseHttp.get<{ success: boolean; tenant: Tenant }>(
        `/tenants/${tenantId}`,
        {
          headers: {
            accept: "application/json",
          },
        }
      );
      return response.data.tenant;
    } catch (error: any) {
      console.error("Get tenant details error:", error);
      throw new Error(
        error.response?.data?.detail || "Failed to fetch tenant details"
      );
    }
  }

  /**
   * Create a new tenant with query parameters
   */
  async createTenant(
    tenantData: CreateTenantData
  ): Promise<CreateTenantResponse> {
    try {
      // Use query parameters as shown in the curl command
      const params = new URLSearchParams({
        tenant_name: tenantData.business_name,
        tenant_slug: tenantData.company_slug,
        product: tenantData.product,
        username: tenantData.username,
        org_type: tenantData.org_type,
      });

      const response = await baseHttp.post<CreateTenantResponse>(
        `/create-tenent?${params.toString()}`,
        "", // Empty body as per curl command
        {
          headers: {
            accept: "application/json",
          },
        }
      );
      return response.data;
    } catch (error: any) {
      console.error("Create tenant error:", error);
      throw new Error(
        error.response?.data?.detail || "Failed to create tenant"
      );
    }
  }
  fetchOrgTypes = async (): Promise<OrgType[]> => {
    try {
      const response = await baseHttp.get<OrgType[]>("/org-types");
      return response.data;
    } catch (error) {
      console.error("Fetch org types error:", error);
      throw new Error("Failed to fetch organization types");
    }
  };

  /**
   * Logout user and clear stored data
   */
  logout(): void {
    localStorage.removeItem("access_token");
    localStorage.removeItem("user");
    localStorage.removeItem("selected_tenant");
  }

  /**
   * Get current user from localStorage
   */
  getCurrentUser() {
    const userStr = localStorage.getItem("user");
    return userStr ? JSON.parse(userStr) : null;
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    return !!localStorage.getItem("access_token");
  }

  /**
   * Check if user is admin
   */
  isAdmin(): boolean {
    const user = this.getCurrentUser();
    return user?.role === "admin";
  }
}

export default new AuthService();
